1:"$Sreact.fragment"
2:"$Sreact.suspense"
3:I[99030,["8888","static/chunks/supabase-55776fae-704411a5287a30d1.js","1459","static/chunks/supabase-0d08456b-4fc32f92c04ec1b2.js","5738","static/chunks/utils-b5e5c4bc3c833bb1.js","9968","static/chunks/ui-components-b80f15b3-d706430e6d29d4e9.js","6060","static/chunks/ui-components-3acb5f41-6e52bae6f8fb52ff.js","4755","static/chunks/vendors-0925edb1-7c86a3ef9dc28918.js","563","static/chunks/vendors-ad6a2f20-003cc44fa9df4a8c.js","2662","static/chunks/vendors-04fef8b0-2eb254c773caab4c.js","8669","static/chunks/vendors-7ec938a2-dcd0e26d54d6db3d.js","4703","static/chunks/vendors-8b9b2362-31ce7ba97daac261.js","3269","static/chunks/vendors-6b948b9f-694e4a46d5a2b2c6.js","9173","static/chunks/vendors-89d5c698-4e0ade9a114e8bac.js","9219","static/chunks/vendors-eb2fbf4c-89792ab6af531ea3.js","9491","static/chunks/9491-95130f910476d7a0.js","9558","static/chunks/app/layout-6db38baa55bdc65a.js"],"default"]
4:I[52469,["8888","static/chunks/supabase-55776fae-704411a5287a30d1.js","1459","static/chunks/supabase-0d08456b-4fc32f92c04ec1b2.js","5738","static/chunks/utils-b5e5c4bc3c833bb1.js","9968","static/chunks/ui-components-b80f15b3-d706430e6d29d4e9.js","6060","static/chunks/ui-components-3acb5f41-6e52bae6f8fb52ff.js","4755","static/chunks/vendors-0925edb1-7c86a3ef9dc28918.js","563","static/chunks/vendors-ad6a2f20-003cc44fa9df4a8c.js","2662","static/chunks/vendors-04fef8b0-2eb254c773caab4c.js","8669","static/chunks/vendors-7ec938a2-dcd0e26d54d6db3d.js","4703","static/chunks/vendors-8b9b2362-31ce7ba97daac261.js","3269","static/chunks/vendors-6b948b9f-694e4a46d5a2b2c6.js","9173","static/chunks/vendors-89d5c698-4e0ade9a114e8bac.js","9219","static/chunks/vendors-eb2fbf4c-89792ab6af531ea3.js","9491","static/chunks/9491-95130f910476d7a0.js","9558","static/chunks/app/layout-6db38baa55bdc65a.js"],"default"]
5:I[38050,["8888","static/chunks/supabase-55776fae-704411a5287a30d1.js","1459","static/chunks/supabase-0d08456b-4fc32f92c04ec1b2.js","5738","static/chunks/utils-b5e5c4bc3c833bb1.js","9968","static/chunks/ui-components-b80f15b3-d706430e6d29d4e9.js","6060","static/chunks/ui-components-3acb5f41-6e52bae6f8fb52ff.js","4755","static/chunks/vendors-0925edb1-7c86a3ef9dc28918.js","563","static/chunks/vendors-ad6a2f20-003cc44fa9df4a8c.js","2662","static/chunks/vendors-04fef8b0-2eb254c773caab4c.js","8669","static/chunks/vendors-7ec938a2-dcd0e26d54d6db3d.js","4703","static/chunks/vendors-8b9b2362-31ce7ba97daac261.js","3269","static/chunks/vendors-6b948b9f-694e4a46d5a2b2c6.js","9173","static/chunks/vendors-89d5c698-4e0ade9a114e8bac.js","9219","static/chunks/vendors-eb2fbf4c-89792ab6af531ea3.js","9491","static/chunks/9491-95130f910476d7a0.js","9558","static/chunks/app/layout-6db38baa55bdc65a.js"],"default"]
6:I[35462,["8888","static/chunks/supabase-55776fae-704411a5287a30d1.js","1459","static/chunks/supabase-0d08456b-4fc32f92c04ec1b2.js","5738","static/chunks/utils-b5e5c4bc3c833bb1.js","9968","static/chunks/ui-components-b80f15b3-d706430e6d29d4e9.js","6060","static/chunks/ui-components-3acb5f41-6e52bae6f8fb52ff.js","4755","static/chunks/vendors-0925edb1-7c86a3ef9dc28918.js","563","static/chunks/vendors-ad6a2f20-003cc44fa9df4a8c.js","2662","static/chunks/vendors-04fef8b0-2eb254c773caab4c.js","8669","static/chunks/vendors-7ec938a2-dcd0e26d54d6db3d.js","4703","static/chunks/vendors-8b9b2362-31ce7ba97daac261.js","3269","static/chunks/vendors-6b948b9f-694e4a46d5a2b2c6.js","9173","static/chunks/vendors-89d5c698-4e0ade9a114e8bac.js","9219","static/chunks/vendors-eb2fbf4c-89792ab6af531ea3.js","9491","static/chunks/9491-95130f910476d7a0.js","9558","static/chunks/app/layout-6db38baa55bdc65a.js"],"default"]
7:I[87555,[],""]
8:I[31295,[],""]
9:I[6874,["4755","static/chunks/vendors-0925edb1-7c86a3ef9dc28918.js","563","static/chunks/vendors-ad6a2f20-003cc44fa9df4a8c.js","2662","static/chunks/vendors-04fef8b0-2eb254c773caab4c.js","8669","static/chunks/vendors-7ec938a2-dcd0e26d54d6db3d.js","4703","static/chunks/vendors-8b9b2362-31ce7ba97daac261.js","3269","static/chunks/vendors-6b948b9f-694e4a46d5a2b2c6.js","9173","static/chunks/vendors-89d5c698-4e0ade9a114e8bac.js","9219","static/chunks/vendors-eb2fbf4c-89792ab6af531ea3.js","4345","static/chunks/app/not-found-8a01e07541050b24.js"],""]
a:I[48031,["8888","static/chunks/supabase-55776fae-704411a5287a30d1.js","1459","static/chunks/supabase-0d08456b-4fc32f92c04ec1b2.js","5738","static/chunks/utils-b5e5c4bc3c833bb1.js","9968","static/chunks/ui-components-b80f15b3-d706430e6d29d4e9.js","6060","static/chunks/ui-components-3acb5f41-6e52bae6f8fb52ff.js","4755","static/chunks/vendors-0925edb1-7c86a3ef9dc28918.js","563","static/chunks/vendors-ad6a2f20-003cc44fa9df4a8c.js","2662","static/chunks/vendors-04fef8b0-2eb254c773caab4c.js","8669","static/chunks/vendors-7ec938a2-dcd0e26d54d6db3d.js","4703","static/chunks/vendors-8b9b2362-31ce7ba97daac261.js","3269","static/chunks/vendors-6b948b9f-694e4a46d5a2b2c6.js","9173","static/chunks/vendors-89d5c698-4e0ade9a114e8bac.js","9219","static/chunks/vendors-eb2fbf4c-89792ab6af531ea3.js","9491","static/chunks/9491-95130f910476d7a0.js","9558","static/chunks/app/layout-6db38baa55bdc65a.js"],"SpeedInsights"]
b:I[69243,["8888","static/chunks/supabase-55776fae-704411a5287a30d1.js","1459","static/chunks/supabase-0d08456b-4fc32f92c04ec1b2.js","5738","static/chunks/utils-b5e5c4bc3c833bb1.js","9968","static/chunks/ui-components-b80f15b3-d706430e6d29d4e9.js","6060","static/chunks/ui-components-3acb5f41-6e52bae6f8fb52ff.js","4755","static/chunks/vendors-0925edb1-7c86a3ef9dc28918.js","563","static/chunks/vendors-ad6a2f20-003cc44fa9df4a8c.js","2662","static/chunks/vendors-04fef8b0-2eb254c773caab4c.js","8669","static/chunks/vendors-7ec938a2-dcd0e26d54d6db3d.js","4703","static/chunks/vendors-8b9b2362-31ce7ba97daac261.js","3269","static/chunks/vendors-6b948b9f-694e4a46d5a2b2c6.js","9173","static/chunks/vendors-89d5c698-4e0ade9a114e8bac.js","9219","static/chunks/vendors-eb2fbf4c-89792ab6af531ea3.js","9491","static/chunks/9491-95130f910476d7a0.js","9558","static/chunks/app/layout-6db38baa55bdc65a.js"],""]
d:I[90894,[],"ClientPageRoot"]
e:I[93659,["7125","static/chunks/landing-components-388e1a4b8291cb52.js","5738","static/chunks/utils-b5e5c4bc3c833bb1.js","9968","static/chunks/ui-components-b80f15b3-d706430e6d29d4e9.js","6060","static/chunks/ui-components-3acb5f41-6e52bae6f8fb52ff.js","4755","static/chunks/vendors-0925edb1-7c86a3ef9dc28918.js","563","static/chunks/vendors-ad6a2f20-003cc44fa9df4a8c.js","2662","static/chunks/vendors-04fef8b0-2eb254c773caab4c.js","8669","static/chunks/vendors-7ec938a2-dcd0e26d54d6db3d.js","4703","static/chunks/vendors-8b9b2362-31ce7ba97daac261.js","3269","static/chunks/vendors-6b948b9f-694e4a46d5a2b2c6.js","9173","static/chunks/vendors-89d5c698-4e0ade9a114e8bac.js","9219","static/chunks/vendors-eb2fbf4c-89792ab6af531ea3.js","7471","static/chunks/app/about-developer/page-75ca86c97370d62d.js"],"default"]
11:I[59665,[],"OutletBoundary"]
14:I[74911,[],"AsyncMetadataOutlet"]
16:I[59665,[],"ViewportBoundary"]
18:I[59665,[],"MetadataBoundary"]
1a:I[26614,[],""]
:HL["/_next/static/css/5b576904c612405e.css","style"]
:HL["/_next/static/css/5af7ef1efb76955d.css","style"]
:HL["/_next/static/css/32066a333285873e.css","style"]
c:T569,
            if ('serviceWorker' in navigator) {
              window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                  .then(function(registration) {
                    console.log('✅ Service Worker registered successfully');

                    // Preload critical data after SW is ready
                    if (window.location.pathname === '/') {
                      // Preload landing page data
                      fetch('/api/system-status').catch(() => {});

                      // Prefetch all critical pages immediately
                      setTimeout(() => {
                        const criticalPages = ['/features', '/pricing', '/about', '/auth/signin', '/auth/signup'];
                        criticalPages.forEach(page => {
                          const link = document.createElement('link');
                          link.rel = 'prefetch';
                          link.href = page;
                          document.head.appendChild(link);
                        });
                      }, 500); // Much faster prefetching
                    }
                  })
                  .catch(function(registrationError) {
                    console.warn('⚠️ Service Worker registration failed:', registrationError);
                  });
              });
            }
          0:{"P":null,"b":"EV-23L5bk2unzKDWl1SjK","p":"","c":["","about-developer"],"i":false,"f":[[["",{"children":["about-developer",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/5b576904c612405e.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/5af7ef1efb76955d.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","link","2",{"rel":"stylesheet","href":"/_next/static/css/32066a333285873e.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","className":"__variable_e8ce0c","children":[["$","head",null,{"children":[["$","link",null,{"rel":"icon","href":"/RouKey_Logo_GLOW.png","type":"image/png","sizes":"32x32"}],["$","link",null,{"rel":"icon","href":"/RouKey_Logo_GLOW.png","type":"image/png","sizes":"16x16"}],["$","link",null,{"rel":"apple-touch-icon","href":"/RouKey_Logo_GLOW.png","sizes":"180x180"}],["$","link",null,{"rel":"manifest","href":"/manifest.json"}],["$","link",null,{"rel":"preload","href":"/api/custom-configs","as":"fetch","crossOrigin":"anonymous"}],["$","link",null,{"rel":"preload","href":"/api/system-status","as":"fetch","crossOrigin":"anonymous"}],["$","link",null,{"rel":"dns-prefetch","href":"//fonts.googleapis.com"}],["$","link",null,{"rel":"preconnect","href":"https://fonts.gstatic.com","crossOrigin":""}],["$","script",null,{"type":"application/ld+json","dangerouslySetInnerHTML":{"__html":"{\"@context\":\"https://schema.org\",\"@type\":\"Organization\",\"name\":\"RouKey\",\"url\":\"https://roukey.online\",\"logo\":\"https://roukey.online/RouKey_Logo_GLOW.png\",\"description\":\"Smart AI routing platform that connects 50+ AI providers using your own API keys. Intelligent routing, fallback protection, and cost optimization.\",\"foundingDate\":\"2025\",\"founder\":{\"@type\":\"Person\",\"name\":\"Okoro David Chukwunyerem\"},\"sameAs\":[\"https://www.producthunt.com/products/roukey\"],\"contactPoint\":{\"@type\":\"ContactPoint\",\"email\":\"<EMAIL>\",\"contactType\":\"customer service\"}}"}}],["$","link",null,{"rel":"prefetch","href":"/dashboard"}],["$","link",null,{"rel":"prefetch","href":"/playground"}],["$","link",null,{"rel":"prefetch","href":"/logs"}],["$","link",null,{"rel":"prefetch","href":"/my-models"}]]}],["$","body",null,{"className":"font-sans antialiased bg-[#1B1C1D]","children":[["$","$2",null,{"fallback":null,"children":["$","$L3",null,{}]}],["$","$L4",null,{}],["$","$L5",null,{"enableUserBehaviorTracking":true,"enableNavigationTracking":true,"enableInteractionTracking":true}],["$","$L6",null,{"children":["$","$L7",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L8",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","div",null,{"className":"min-h-screen bg-black flex items-center justify-center","children":["$","div",null,{"className":"text-center","children":[["$","h1",null,{"className":"text-6xl font-bold text-orange-500 mb-4","children":"404"}],["$","h2",null,{"className":"text-2xl font-semibold text-white mb-4","children":"Page Not Found"}],["$","p",null,{"className":"text-gray-400 mb-8","children":"The page you're looking for doesn't exist."}],["$","$L9",null,{"href":"/","className":"bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg transition-colors","children":"Go Home"}]]}]}],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","$La",null,{}],false,["$","$Lb",null,{"id":"sw-register","strategy":"afterInteractive","children":"$c"}]]}]]}]]}],{"children":["about-developer",["$","$1","c",{"children":[null,["$","$L7",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L8",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$Ld",null,{"Component":"$e","searchParams":{},"params":{},"promises":["$@f","$@10"]}],null,["$","$L11",null,{"children":["$L12","$L13",["$","$L14",null,{"promise":"$@15"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","zjmIYDRduEndve7mbGtBQv",{"children":[["$","$L16",null,{"children":"$L17"}],null]}],["$","$L18",null,{"children":"$L19"}]]}],false]],"m":"$undefined","G":["$1a","$undefined"],"s":false,"S":true}
1b:I[74911,[],"AsyncMetadata"]
f:{}
10:{}
19:["$","div",null,{"hidden":true,"children":["$","$2",null,{"fallback":null,"children":["$","$L1b",null,{"promise":"$@1c"}]}]}]
13:null
17:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"}]]
12:null
15:{"metadata":[["$","title","0",{"children":"About the Developer - Okoro David Chukwunyerem | RouKey"}],["$","meta","1",{"name":"description","content":"Meet Okoro David Chukwunyerem, the solo developer behind RouKey. Learn about his journey from startup failure to building the ultimate AI routing platform."}],["$","meta","2",{"name":"author","content":"Okoro David Chukwunyerem"}],["$","meta","3",{"name":"keywords","content":"Okoro David Chukwunyerem, RouKey developer, AI gateway founder, solo developer, startup journey"}],["$","meta","4",{"name":"creator","content":"Okoro David Chukwunyerem"}],["$","meta","5",{"name":"publisher","content":"RouKey"}],["$","meta","6",{"name":"robots","content":"index, follow"}],["$","meta","7",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","meta","8",{"property":"og:title","content":"About the Developer - Okoro David Chukwunyerem | RouKey"}],["$","meta","9",{"property":"og:description","content":"Meet the solo developer behind RouKey - from startup failure to building the ultimate AI routing platform."}],["$","meta","10",{"property":"og:url","content":"https://roukey.online/about-developer"}],["$","meta","11",{"property":"og:image","content":"https://roukey.online/founder.jpg"}],["$","meta","12",{"property":"og:image:width","content":"1200"}],["$","meta","13",{"property":"og:image:height","content":"630"}],["$","meta","14",{"property":"og:image:alt","content":"Okoro David Chukwunyerem - RouKey Founder"}],["$","meta","15",{"property":"og:type","content":"website"}],["$","meta","16",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","17",{"name":"twitter:title","content":"About the Developer - Okoro David Chukwunyerem | RouKey"}],["$","meta","18",{"name":"twitter:description","content":"Meet the solo developer behind RouKey - from startup failure to building the ultimate AI routing platform."}],["$","meta","19",{"name":"twitter:image","content":"https://roukey.online/founder.jpg"}],["$","link","20",{"rel":"shortcut icon","href":"/RouKey_Logo_GLOW.png"}],["$","link","21",{"rel":"icon","href":"/RouKey_Logo_GLOW.png","sizes":"32x32","type":"image/png"}],["$","link","22",{"rel":"icon","href":"/RouKey_Logo_GLOW.png","sizes":"16x16","type":"image/png"}],["$","link","23",{"rel":"apple-touch-icon","href":"/RouKey_Logo_GLOW.png","sizes":"180x180","type":"image/png"}]],"error":null,"digest":"$undefined"}
1c:{"metadata":"$15:metadata","error":null,"digest":"$undefined"}
