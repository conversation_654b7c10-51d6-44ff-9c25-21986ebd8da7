import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { STRIPE_ENV_INFO, STRIPE_KEYS, STRIPE_PRICE_IDS } from '@/lib/stripe-config';
import Stripe from 'stripe';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const stripe = new Stripe(STRIPE_KEYS.secretKey, {
  apiVersion: '2025-02-24.acacia',
});

function getTierFromPriceId(priceId: string | undefined): string {
  if (!priceId) return 'free';

  switch (priceId) {
    case STRIPE_PRICE_IDS.FREE:
      return 'free';
    case STRIPE_PRICE_IDS.STARTER:
      return 'starter';
    case STRIPE_PRICE_IDS.PROFESSIONAL:
      return 'professional';
    case STRIPE_PRICE_IDS.ENTERPRISE:
      return 'enterprise';
    default:
      console.warn(`Unknown price ID: ${priceId}, defaulting to free`);
      return 'free';
  }
}

async function getUserTierFromStripe(userId: string): Promise<{ tier: string; subscription?: Stripe.Subscription }> {
  try {
    // Get user's subscription from database to get Stripe subscription ID
    const { data: subscription, error } = await supabase
      .from('subscriptions')
      .select('stripe_subscription_id')
      .eq('user_id', userId)
      .single();

    if (error || !subscription || !subscription.stripe_subscription_id) {
      console.log(`No Stripe subscription found for user ${userId}, defaulting to free`);
      return { tier: 'free' };
    }

    // Fetch the actual subscription from Stripe
    const stripeSubscription = await stripe.subscriptions.retrieve(subscription.stripe_subscription_id);

    // Check if subscription is active
    if (stripeSubscription.status !== 'active') {
      console.log(`Stripe subscription ${subscription.stripe_subscription_id} is not active: ${stripeSubscription.status}`);
      return { tier: 'free' };
    }

    // Get the price ID and determine tier
    const priceId = stripeSubscription.items.data[0]?.price.id;
    const tier = getTierFromPriceId(priceId);

    console.log(`✅ User ${userId} actual Stripe tier: ${tier} (price: ${priceId})`);

    // Update database in background to keep it in sync
    updateDatabaseTier(userId, tier, stripeSubscription).catch(err =>
      console.error('Background database update failed:', err)
    );

    return { tier, subscription: stripeSubscription };

  } catch (error) {
    console.error(`Error fetching Stripe tier for user ${userId}:`, error);
    return { tier: 'free' }; // Fallback to free on error
  }
}

async function updateDatabaseTier(userId: string, tier: string, stripeSubscription: Stripe.Subscription) {
  try {
    // Update subscription table
    await supabase
      .from('subscriptions')
      .update({
        tier,
        status: stripeSubscription.status,
        current_period_start: new Date(stripeSubscription.current_period_start * 1000).toISOString(),
        current_period_end: new Date(stripeSubscription.current_period_end * 1000).toISOString(),
        cancel_at_period_end: stripeSubscription.cancel_at_period_end,
        is_free_tier: tier === 'free',
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId);

    // Update user profile
    await supabase
      .from('user_profiles')
      .update({
        subscription_tier: tier,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    console.log(`📝 Database updated for user ${userId} to tier: ${tier}`);
  } catch (error) {
    console.error('Error updating database:', error);
  }
}

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');

    console.log('🔍 Subscription status API called for user:', userId);

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId parameter' },
        { status: 400 }
      );
    }

    // Check for network connectivity issues
    if (process.env.SUPABASE_SERVER_CONNECTIVITY_ISSUE === 'true') {
      console.log('Network connectivity issue detected, returning fallback response');
      return NextResponse.json({
        hasActiveSubscription: true,
        tier: 'free',
        status: 'active',
        currentPeriodEnd: null,
        cancelAtPeriodEnd: false,
        isFree: true,
        fallback: true,
        message: 'Using fallback due to network connectivity issues'
      });
    }

    // Get tier directly from Stripe (source of truth)
    console.log(`🔄 Getting tier for user ${userId} from Stripe...`);
    const { tier, subscription: stripeSubscription } = await getUserTierFromStripe(userId);

    // Get database subscription for additional details
    const { data: dbSubscription } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();

    console.log(`✅ Stripe tier: ${tier} for user ${userId}`);

    // If user has a Stripe subscription, use those details
    if (stripeSubscription) {
      const isActive = stripeSubscription.status === 'active';
      const currentPeriodEnd = new Date(stripeSubscription.current_period_end * 1000);
      const isExpired = currentPeriodEnd < new Date();

      console.log('GET subscription-status - Stripe subscription found:', {
        userId,
        tier,
        status: stripeSubscription.status,
        isActive,
        isExpired,
        hasActiveSubscription: isActive && !isExpired
      });

      return NextResponse.json({
        hasActiveSubscription: isActive && !isExpired,
        tier,
        status: stripeSubscription.status,
        currentPeriodEnd: stripeSubscription.current_period_end,
        currentPeriodStart: stripeSubscription.current_period_start,
        cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
        stripeCustomerId: stripeSubscription.customer,
        stripeSubscriptionId: stripeSubscription.id,
        isFree: tier === 'free',
        source: 'stripe' // Indicate this came from Stripe
      });
    } else {
      // Free tier user
      console.log('GET subscription-status - Free tier user:', { userId, tier });
      return NextResponse.json({
        hasActiveSubscription: false,
        tier: 'free',
        status: null,
        currentPeriodEnd: null,
        cancelAtPeriodEnd: false,
        isFree: true,
        source: 'stripe' // Still checked Stripe, just no subscription
      });
    }

  } catch (error) {
    console.error('Error fetching subscription status:', error);

    // If there's a network error, return fallback response
    if (error instanceof Error && (error.message.includes('fetch failed') || error.message.includes('network'))) {
      console.log('Network error detected, returning fallback response');
      return NextResponse.json({
        hasActiveSubscription: true,
        tier: 'free',
        status: 'active',
        currentPeriodEnd: null,
        cancelAtPeriodEnd: false,
        isFree: true,
        fallback: true,
        message: 'Using fallback due to network error'
      });
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const { userId } = await req.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId' },
        { status: 400 }
      );
    }

    // Check for network connectivity issues
    if (process.env.SUPABASE_SERVER_CONNECTIVITY_ISSUE === 'true') {
      console.log('Network connectivity issue detected, returning fallback usage response');
      return NextResponse.json({
        tier: 'free',
        usage: {
          configurations: 0,
          apiKeys: 0,
          apiRequests: 0,
        },
        limits: getTierLimits('free'),
        canCreateConfig: true,
        canCreateApiKey: true,
        fallback: true,
        message: 'Using fallback due to network connectivity issues'
      });
    }

    // Get current usage for the user
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format

    const { data: usage, error: usageError } = await supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', userId)
      .eq('month_year', currentMonth)
      .single();

    // Get tier directly from Stripe (source of truth)
    console.log(`🔄 Getting tier for user ${userId} from Stripe for usage check...`);
    const { tier } = await getUserTierFromStripe(userId);

    console.log('POST subscription-status - User tier from Stripe:', {
      userId,
      stripeTier: tier,
      source: 'stripe'
    });

    // Get configuration and API key counts
    const { count: configCount } = await supabase
      .from('custom_api_configs')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    const { count: apiKeyCount } = await supabase
      .from('api_keys')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    // Note: Workflow features will be added in future updates

    // Calculate tier limits
    const limits = getTierLimits(tier);

    return NextResponse.json({
      tier,
      usage: {
        configurations: configCount || 0,
        apiKeys: apiKeyCount || 0,
        apiRequests: usage?.api_requests_count || 0,
      },
      limits,
      canCreateConfig: (configCount || 0) < limits.configurations,
      canCreateApiKey: (apiKeyCount || 0) < limits.apiKeysPerConfig,
    });

  } catch (error) {
    console.error('Error fetching usage status:', error);

    // If there's a network error, return fallback response
    if (error instanceof Error && (error.message.includes('fetch failed') || error.message.includes('network'))) {
      console.log('Network error detected, returning fallback usage response');
      return NextResponse.json({
        tier: 'free',
        usage: {
          configurations: 0,
          apiKeys: 0,
          apiRequests: 0,
        },
        limits: getTierLimits('free'),
        canCreateConfig: true,
        canCreateApiKey: true,
        fallback: true,
        message: 'Using fallback due to network error'
      });
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function getTierLimits(tier: string) {
  switch (tier) {
    case 'free':
      return {
        configurations: 1,
        apiKeysPerConfig: 3,
        apiRequests: 999999, // Unlimited
        canUseAdvancedRouting: false,
        canUseCustomRoles: false,
        maxCustomRoles: 0,
        canUsePromptEngineering: false,
        canUseKnowledgeBase: false,
        knowledgeBaseDocuments: 0,
        canUseSemanticCaching: false,
      };
    case 'starter':
      return {
        configurations: 15,
        apiKeysPerConfig: 5,
        apiRequests: 999999, // Unlimited
        canUseAdvancedRouting: true,
        canUseCustomRoles: true,
        maxCustomRoles: 3,
        canUsePromptEngineering: true,
        canUseKnowledgeBase: false,
        knowledgeBaseDocuments: 0,
        canUseSemanticCaching: false,
      };
    case 'professional':
      return {
        configurations: 999999, // Unlimited
        apiKeysPerConfig: 999999, // Unlimited
        apiRequests: 999999, // Unlimited
        canUseAdvancedRouting: true,
        canUseCustomRoles: true,
        maxCustomRoles: 999999,
        canUsePromptEngineering: true,
        canUseKnowledgeBase: true,
        knowledgeBaseDocuments: 5,
        canUseSemanticCaching: true,
      };
    case 'enterprise':
      return {
        configurations: 999999, // Unlimited
        apiKeysPerConfig: 999999, // Unlimited
        apiRequests: 999999, // Unlimited
        canUseAdvancedRouting: true,
        canUseCustomRoles: true,
        maxCustomRoles: 999999,
        canUsePromptEngineering: true,
        canUseKnowledgeBase: true,
        knowledgeBaseDocuments: 999999,
        canUseSemanticCaching: true,
      };
    default:
      return {
        configurations: 1,
        apiKeysPerConfig: 3,
        apiRequests: 999999,
        canUseAdvancedRouting: false,
        canUseCustomRoles: false,
        maxCustomRoles: 0,
        canUsePromptEngineering: false,
        canUseKnowledgeBase: false,
        knowledgeBaseDocuments: 0,
        canUseSemanticCaching: false,
      };
  }
}
