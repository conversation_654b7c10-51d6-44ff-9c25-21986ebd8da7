(()=>{var e={};e.id=7175,e.ids=[7175],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},25620:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>b,routeModule:()=>f,serverHooks:()=>x,workAsyncStorage:()=>P,workUnitAsyncStorage:()=>T});var t={};s.r(t),s.d(t,{GET:()=>S,POST:()=>I});var i=s(96559),n=s(48088),o=s(37719),u=s(32190),a=s(80726),c=s(64745),p=s(39398),E=s(94473);let _=new c.A(E.Lj.secretKey,{apiVersion:"2025-02-24.acacia"}),d=(0,p.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function R(e){try{let{data:s,error:t}=await d.from("subscriptions").select("stripe_subscription_id, stripe_customer_id").eq("user_id",e).single();if(t||!s||!s.stripe_subscription_id)return"free";let i=await _.subscriptions.retrieve(s.stripe_subscription_id);if("active"!==i.status)return"free";var r=i.items.data[0]?.price.id;if(!r)return"free";switch(r){case E.Dm.FREE:return"free";case E.Dm.STARTER:return"starter";case E.Dm.PROFESSIONAL:return"professional";case E.Dm.ENTERPRISE:return"enterprise";default:return"free"}}catch(e){return"free"}}async function l(e){try{let r=await R(e),{data:s,error:t}=await d.from("subscriptions").select("*").eq("user_id",e).single();if(t||!s)return{success:!1,tier:"free",error:"No subscription found in database"};let i={tier:r,is_free_tier:"free"===r,updated_at:new Date().toISOString()};if(s.stripe_subscription_id&&"free"!==r)try{let e=await _.subscriptions.retrieve(s.stripe_subscription_id);i={...i,status:e.status,current_period_start:new Date(1e3*e.current_period_start).toISOString(),current_period_end:new Date(1e3*e.current_period_end).toISOString(),cancel_at_period_end:e.cancel_at_period_end}}catch(e){}let{error:n}=await d.from("subscriptions").update(i).eq("user_id",e);if(n)return{success:!1,tier:r,error:"Failed to update subscription"};let{error:o}=await d.from("user_profiles").update({subscription_tier:r,updated_at:new Date().toISOString()}).eq("id",e);if(o)return{success:!1,tier:r,error:"Failed to update user profile"};return{success:!0,tier:r}}catch(e){return{success:!1,tier:"free",error:e instanceof Error?e.message:"Unknown error"}}}async function I(e){try{let{email:r,userId:s}=await e.json(),t=s;if(r&&!s){let e=(0,a.H)(),{data:s,error:i}=await e.from("auth.users").select("id, email").eq("email",r).single();if(i||!s)return u.NextResponse.json({error:"User not found"},{status:404});t=s.id}if(!t)return u.NextResponse.json({error:"User ID or email required"},{status:400});let i=await R(t),n=await l(t);return u.NextResponse.json({success:n.success,userId:t,stripeTier:i,syncResult:n,message:n.success?`Successfully synced user to tier: ${n.tier}`:`Sync failed: ${n.error}`})}catch(e){return u.NextResponse.json({error:"Failed to sync subscription",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function S(e){try{let{searchParams:r}=new URL(e.url),s=r.get("email"),t=r.get("userId"),i=t;if(s&&!t){let e=(0,a.H)(),{data:r,error:t}=await e.from("auth.users").select("id, email").eq("email",s).single();if(t||!r)return u.NextResponse.json({error:"User not found"},{status:404});i=r.id}if(!i)return u.NextResponse.json({error:"User ID or email required"},{status:400});let n=await R(i);return u.NextResponse.json({userId:i,stripeTier:n,message:`Current Stripe tier: ${n}`})}catch(e){return u.NextResponse.json({error:"Failed to check tier",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let f=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/user/sync-subscription/route",pathname:"/api/user/sync-subscription",filename:"route",bundlePath:"app/api/user/sync-subscription/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\user\\sync-subscription\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:P,workUnitAsyncStorage:T,serverHooks:x}=f;function b(){return(0,o.patchFetch)({workAsyncStorage:P,workUnitAsyncStorage:T})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80726:(e,r,s)=>{"use strict";s.d(r,{H:()=>i});var t=s(39398);function i(){return(0,t.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}})}},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94473:(e,r,s)=>{"use strict";s.d(r,{Dm:()=>i,Lj:()=>t,Zu:()=>n});let t={publishableKey:process.env.STRIPE_LIVE_PUBLISHABLE_KEY,secretKey:process.env.STRIPE_LIVE_SECRET_KEY,webhookSecret:process.env.STRIPE_LIVE_WEBHOOK_SECRET},i={FREE:process.env.STRIPE_LIVE_FREE_PRICE_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRICE_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID},n={FREE:process.env.STRIPE_LIVE_FREE_PRODUCT_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRODUCT_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID};process.env.STRIPE_LIVE_FREE_PRICE_ID,process.env.STRIPE_LIVE_STARTER_PRICE_ID,process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID,t.publishableKey&&t.publishableKey.substring(0,20),t.secretKey&&t.secretKey.substring(0,20),t.webhookSecret&&t.webhookSecret.substring(0,15)},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,580,9398,4745],()=>s(25620));module.exports=t})();