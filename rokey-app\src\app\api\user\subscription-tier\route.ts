import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { createServiceRoleClient } from '@/lib/supabase/service-role';
import Stripe from 'stripe';
import { STRIPE_KEYS, STRIPE_PRICE_IDS } from '@/lib/stripe-config';

const stripe = new Stripe(STRIPE_KEYS.secretKey, {
  apiVersion: '2025-02-24.acacia',
});

function getTierFromPriceId(priceId: string | undefined): string {
  if (!priceId) return 'free';

  switch (priceId) {
    case STRIPE_PRICE_IDS.FREE:
      return 'free';
    case STRIPE_PRICE_IDS.STARTER:
      return 'starter';
    case STRIPE_PRICE_IDS.PROFESSIONAL:
      return 'professional';
    case STRIPE_PRICE_IDS.ENTERPRISE:
      return 'enterprise';
    default:
      console.warn(`Unknown price ID: ${priceId}, defaulting to free`);
      return 'free';
  }
}

async function getUserTierFromStripe(userId: string): Promise<string> {
  try {
    const serviceSupabase = createServiceRoleClient();

    // Get user's subscription from database to get Stripe subscription ID
    const { data: subscription, error } = await serviceSupabase
      .from('subscriptions')
      .select('stripe_subscription_id')
      .eq('user_id', userId)
      .single();

    if (error || !subscription || !subscription.stripe_subscription_id) {
      console.log(`No Stripe subscription found for user ${userId}, defaulting to free`);
      return 'free';
    }

    // Fetch the actual subscription from Stripe
    const stripeSubscription = await stripe.subscriptions.retrieve(subscription.stripe_subscription_id);

    // Check if subscription is active
    if (stripeSubscription.status !== 'active') {
      console.log(`Stripe subscription ${subscription.stripe_subscription_id} is not active: ${stripeSubscription.status}`);
      return 'free';
    }

    // Get the price ID and determine tier
    const priceId = stripeSubscription.items.data[0]?.price.id;
    const tier = getTierFromPriceId(priceId);

    console.log(`✅ User ${userId} actual Stripe tier: ${tier} (price: ${priceId})`);

    // Update database in background to keep it in sync
    updateDatabaseTier(userId, tier, stripeSubscription).catch(err =>
      console.error('Background database update failed:', err)
    );

    return tier;

  } catch (error) {
    console.error(`Error fetching Stripe tier for user ${userId}:`, error);
    return 'free'; // Fallback to free on error
  }
}

async function updateDatabaseTier(userId: string, tier: string, stripeSubscription: Stripe.Subscription) {
  try {
    const serviceSupabase = createServiceRoleClient();

    // Update subscription table
    await serviceSupabase
      .from('subscriptions')
      .update({
        tier,
        status: stripeSubscription.status,
        current_period_start: new Date(stripeSubscription.current_period_start * 1000).toISOString(),
        current_period_end: new Date(stripeSubscription.current_period_end * 1000).toISOString(),
        cancel_at_period_end: stripeSubscription.cancel_at_period_end,
        is_free_tier: tier === 'free',
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId);

    // Update user profile
    await serviceSupabase
      .from('user_profiles')
      .update({
        subscription_tier: tier,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    console.log(`📝 Database updated for user ${userId} to tier: ${tier}`);
  } catch (error) {
    console.error('Error updating database:', error);
  }
}

// GET /api/user/subscription-tier - Get user's current subscription tier (from Stripe)
export async function GET(request: NextRequest) {
  const supabase = createSupabaseServerClientFromRequest(request);

  try {
    // Authenticate user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log(`🔍 Getting tier for user ${user.id} from Stripe...`);

    // Get tier directly from Stripe (source of truth)
    const tier = await getUserTierFromStripe(user.id);

    console.log(`✅ Returning tier: ${tier} for user ${user.id}`);

    return NextResponse.json({ tier });

  } catch (error) {
    console.error('Error in GET /api/user/subscription-tier:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
