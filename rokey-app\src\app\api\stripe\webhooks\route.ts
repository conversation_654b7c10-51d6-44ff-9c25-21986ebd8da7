import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';
import { STRIPE_KEYS, STRIPE_PRICE_IDS } from '@/lib/stripe-config';

const stripe = new Stripe(STRIPE_KEYS.secretKey, {
  apiVersion: '2025-02-24.acacia',
});

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET() {
  return new Response('Stripe webhook endpoint is active', {
    status: 200,
    headers: { 'Content-Type': 'text/plain' }
  });
}

export async function POST(req: NextRequest) {
  const body = await req.text();
  const signature = req.headers.get('stripe-signature');

  if (!signature) {
    console.error('Missing stripe-signature header');
    return NextResponse.json({ error: 'Missing signature' }, { status: 400 });
  }

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      STRIPE_KEYS.webhookSecret
    );
  } catch (err) {
    console.error('Webhook signature verification failed:', err);
    return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
  }

  console.log('Received webhook event:', event.type);

  // Log webhook event for debugging
  try {
    await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/debug/webhook-logs`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        eventType: event.type,
        eventId: event.id,
        details: {
          created: event.created,
          livemode: event.livemode,
          object: event.data.object
        }
      })
    }).catch(err => console.log('Failed to log webhook event:', err.message));
  } catch (err) {
    console.log('Failed to log webhook event:', err);
  }

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutCompleted(event.data.object as Stripe.Checkout.Session);
        break;

      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object as Stripe.Subscription);
        break;

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
        break;

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
        break;

      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.Invoice);
        break;

      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.Invoice);
        break;

      case 'checkout.session.expired':
        await handleCheckoutExpired(event.data.object as Stripe.Checkout.Session);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return NextResponse.json({ error: 'Webhook processing failed' }, { status: 500 });
  }
}

async function handleCheckoutCompleted(session: Stripe.Checkout.Session) {
  console.log('Processing checkout completed:', session.id);

  // Check if this is a promotional/free checkout (no subscription created)
  const isPromotionalCheckout = !session.subscription && session.amount_total === 0;

  if (!session.customer && !isPromotionalCheckout) {
    console.error('Missing customer in checkout session');
    return;
  }

  let subscription: Stripe.Subscription | null = null;

  if (session.subscription) {
    // Regular paid subscription
    subscription = await stripe.subscriptions.retrieve(session.subscription as string);
  } else if (isPromotionalCheckout) {
    console.log('Processing promotional/free checkout (no Stripe subscription created)');
  } else {
    console.error('Missing subscription in checkout session and not a promotional checkout');
    return;
  }

  // Check if this is a signup flow
  const isSignup = session.metadata?.signup === 'true';
  const userId = session.metadata?.user_id;

  let finalUserId = userId;

  if (isSignup && session.metadata?.pending_user_data) {
    // This is a new signup - create the Supabase user now
    console.log('Creating Supabase user after successful payment...');

    try {
      const pendingUserData = JSON.parse(session.metadata.pending_user_data);

      // Create the Supabase user with admin privileges
      const { data: newUser, error: userError } = await supabase.auth.admin.createUser({
        email: pendingUserData.email,
        password: pendingUserData.password,
        email_confirm: true, // Auto-confirm email since payment was successful
        user_metadata: {
          full_name: `${pendingUserData.firstName} ${pendingUserData.lastName}`,
          first_name: pendingUserData.firstName,
          last_name: pendingUserData.lastName,
          selected_plan: pendingUserData.plan,
        }
      });

      if (userError) {
        console.error('Error creating Supabase user:', userError);
        throw userError;
      }

      finalUserId = newUser.user.id;
      console.log('Supabase user created successfully:', finalUserId);

    } catch (error) {
      console.error('Error processing signup after payment:', error);
      throw error;
    }
  }

  if (!finalUserId) {
    console.error('Missing user_id in session metadata');
    return;
  }

  // Determine tier from price ID or session metadata
  let tier: string;
  let priceId: string | undefined;

  if (subscription) {
    // Regular subscription - get tier from Stripe subscription
    priceId = subscription.items.data[0]?.price.id;
    tier = getTierFromPriceId(priceId);
  } else if (isPromotionalCheckout) {
    // Promotional checkout - get tier from session metadata
    tier = session.metadata?.tier || 'starter';
    console.log('Using tier from session metadata for promotional checkout:', tier);
  } else {
    console.error('Cannot determine tier - no subscription or promotional checkout');
    return;
  }

  // Create or update subscription record (handle both new users and existing free users upgrading)
  let subscriptionData: any = {
    user_id: finalUserId,
    tier,
    updated_at: new Date().toISOString(),
  };

  if (subscription) {
    // Regular paid subscription
    subscriptionData = {
      ...subscriptionData,
      stripe_subscription_id: subscription.id,
      stripe_customer_id: subscription.customer as string,
      status: subscription.status,
      current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
      current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
      cancel_at_period_end: subscription.cancel_at_period_end,
      is_free_tier: false,
    };
  } else if (isPromotionalCheckout) {
    // Promotional checkout - create local subscription without Stripe IDs
    const now = new Date();
    const oneYearFromNow = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);

    subscriptionData = {
      ...subscriptionData,
      stripe_subscription_id: null,
      stripe_customer_id: session.customer as string || null,
      status: 'active',
      current_period_start: now.toISOString(),
      current_period_end: oneYearFromNow.toISOString(),
      cancel_at_period_end: false,
      is_free_tier: false, // This is a promotional paid tier
    };

    console.log('Creating promotional subscription record:', subscriptionData);
  }

  const { error } = await supabase
    .from('subscriptions')
    .upsert(subscriptionData, {
      onConflict: 'user_id' // Update existing subscription for the user
    });

  if (error) {
    console.error('Error creating subscription record:', error);
    throw error;
  }

  // Create or update user profile with subscription tier and activate user
  const { error: profileError } = await supabase
    .from('user_profiles')
    .upsert({
      id: finalUserId,
      subscription_tier: tier,
      subscription_status: 'active',
      user_status: 'active', // Activate the user account
      updated_at: new Date().toISOString(),
    });

  if (profileError) {
    console.error('Error creating/updating user profile:', profileError);
  } else {
    // Send welcome email immediately when paid user becomes active
    console.log('📧 Sending welcome email to new paid user:', finalUserId);
    try {
      // Get user email from auth
      const { data: authUser } = await supabase.auth.admin.getUserById(finalUserId);

      if (authUser.user?.email) {
        const { sendWelcomeEmail } = await import('@/lib/email/welcomeEmail');
        const emailSent = await sendWelcomeEmail({
          userEmail: authUser.user.email,
          userName: authUser.user.user_metadata?.full_name || 'New User',
          userTier: tier
        });

        if (emailSent) {
          console.log('✅ Welcome email sent successfully to:', authUser.user.email);
        } else {
          console.log('⚠️ Welcome email failed to send to:', authUser.user.email);
        }
      }
    } catch (emailError) {
      console.error('❌ Error sending welcome email:', emailError);
      // Don't fail the webhook if email fails
    }
  }

  // Update user metadata to mark payment as completed
  if (finalUserId) {
    console.log('Updating user metadata to mark payment as completed...');

    const { error: metadataError } = await supabase.auth.admin.updateUserById(finalUserId, {
      user_metadata: {
        payment_status: 'completed',
        plan: tier
      }
    });

    if (metadataError) {
      console.error('Error updating user metadata:', metadataError);
    } else {
      console.log('User metadata updated successfully:', finalUserId);
    }
  }

  console.log(`Subscription created for user ${finalUserId} with tier ${tier}`);
}

async function handleSubscriptionCreated(subscription: Stripe.Subscription) {
  console.log('Processing subscription created:', subscription.id);
  // This is usually handled by checkout.session.completed
  // But we can add additional logic here if needed
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  console.log('🔄 Processing subscription updated:', subscription.id);

  const priceId = subscription.items.data[0]?.price.id;
  const tier = getTierFromPriceId(priceId);

  console.log(`📊 Subscription update details:`, {
    subscriptionId: subscription.id,
    customerId: subscription.customer,
    priceId,
    newTier: tier,
    status: subscription.status,
    currentPeriodStart: subscription.current_period_start,
    currentPeriodEnd: subscription.current_period_end,
    cancelAtPeriodEnd: subscription.cancel_at_period_end
  });

  console.log(`🎯 Price ID mapping: ${priceId} -> ${tier} (is_free_tier: ${tier === 'free'})`);

  // Log subscription update for debugging
  try {
    await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/debug/webhook-logs`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        eventType: 'customer.subscription.updated',
        subscriptionId: subscription.id,
        customerId: subscription.customer,
        priceId,
        tier,
        status: subscription.status,
        details: {
          currentPeriodStart: subscription.current_period_start,
          currentPeriodEnd: subscription.current_period_end,
          cancelAtPeriodEnd: subscription.cancel_at_period_end
        }
      })
    }).catch(err => console.log('Failed to log subscription update:', err.message));
  } catch (err) {
    console.log('Failed to log subscription update:', err);
  }

  const { error } = await supabase
    .from('subscriptions')
    .update({
      tier,
      status: subscription.status,
      current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
      current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
      cancel_at_period_end: subscription.cancel_at_period_end,
      is_free_tier: tier === 'free',
      updated_at: new Date().toISOString(),
    })
    .eq('stripe_subscription_id', subscription.id);

  if (error) {
    console.error('❌ Error updating subscription:', error);
    throw error;
  }

  console.log(`✅ Subscription table updated: ${subscription.id} -> tier: ${tier}, is_free_tier: ${tier === 'free'}`);

  // Update user profile tier
  const { data: subscriptionData, error: subscriptionDataError } = await supabase
    .from('subscriptions')
    .select('user_id')
    .eq('stripe_subscription_id', subscription.id)
    .single();

  if (subscriptionDataError) {
    console.error('Error fetching subscription data for profile update:', subscriptionDataError);
    return;
  }

  if (subscriptionData) {
    console.log(`Updating user profile for user ${subscriptionData.user_id} from tier ? to tier ${tier}`);

    const { error: profileError } = await supabase
      .from('user_profiles')
      .update({
        subscription_tier: tier,
        updated_at: new Date().toISOString()
      })
      .eq('id', subscriptionData.user_id);

    if (profileError) {
      console.error('Error updating user profile tier:', profileError);
    } else {
      console.log(`✅ User profile updated successfully for user ${subscriptionData.user_id} to tier ${tier}`);

      // Verify the update by fetching the updated profile
      const { data: updatedProfile, error: verifyError } = await supabase
        .from('user_profiles')
        .select('subscription_tier')
        .eq('id', subscriptionData.user_id)
        .single();

      if (verifyError) {
        console.error('Error verifying profile update:', verifyError);
      } else {
        console.log(`✅ Profile update verified - current tier: ${updatedProfile.subscription_tier}`);
      }
    }
  } else {
    console.error('No subscription data found for subscription ID:', subscription.id);
  }

  console.log(`Subscription ${subscription.id} updated to tier ${tier} successfully`);
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  console.log('Processing subscription deleted:', subscription.id);

  const { error } = await supabase
    .from('subscriptions')
    .update({
      status: 'canceled',
      updated_at: new Date().toISOString(),
    })
    .eq('stripe_subscription_id', subscription.id);

  if (error) {
    console.error('Error updating deleted subscription:', error);
    throw error;
  }

  // Update user profile to starter tier
  const { data: subscriptionData } = await supabase
    .from('subscriptions')
    .select('user_id')
    .eq('stripe_subscription_id', subscription.id)
    .single();

  if (subscriptionData) {
    await supabase
      .from('user_profiles')
      .update({ subscription_tier: 'starter' })
      .eq('id', subscriptionData.user_id);
  }

  console.log(`Subscription ${subscription.id} canceled`);
}

async function handlePaymentSucceeded(invoice: Stripe.Invoice) {
  console.log('Processing payment succeeded:', invoice.id);
  
  if (invoice.subscription) {
    // Update subscription status to active
    const { error } = await supabase
      .from('subscriptions')
      .update({
        status: 'active',
        updated_at: new Date().toISOString(),
      })
      .eq('stripe_subscription_id', invoice.subscription);

    if (error) {
      console.error('Error updating subscription after payment:', error);
    }
  }
}

async function handlePaymentFailed(invoice: Stripe.Invoice) {
  console.log('Processing payment failed:', invoice.id);

  if (invoice.subscription) {
    // Update subscription status
    const { error } = await supabase
      .from('subscriptions')
      .update({
        status: 'past_due',
        updated_at: new Date().toISOString(),
      })
      .eq('stripe_subscription_id', invoice.subscription);

    if (error) {
      console.error('Error updating subscription after failed payment:', error);
    }
  }
}

async function handleCheckoutExpired(session: Stripe.Checkout.Session) {
  console.log('Processing checkout expired:', session.id);

  // Check if this was a new signup that didn't complete payment
  const userId = session.metadata?.user_id;
  const isSignup = session.metadata?.signup === 'true';

  if (userId && userId !== 'pending_signup') {
    console.log('Checking user for cleanup after checkout expiry:', userId);

    try {
      // Get user to check their payment status
      const { data: user, error: getUserError } = await supabase.auth.admin.getUserById(userId);

      if (getUserError) {
        console.error('Error getting user for cleanup:', getUserError);
        return;
      }

      if (user?.user) {
        const paymentStatus = user.user.user_metadata?.payment_status;

        // Only delete users with pending payment status
        if (paymentStatus === 'pending') {
          console.log('Deleting user with pending payment who abandoned checkout:', userId);

          const { error } = await supabase.auth.admin.deleteUser(userId);

          if (error) {
            console.error('Error deleting abandoned user:', error);
          } else {
            console.log('Successfully deleted abandoned user with pending payment:', userId);
          }
        } else {
          console.log('User does not have pending payment status, skipping deletion:', { userId, paymentStatus });
        }
      }
    } catch (error) {
      console.error('Error in checkout expiry cleanup:', error);
    }
  } else if (userId === 'pending_signup') {
    console.log('Checkout expired for pending signup - no user to delete');
  }

  // Also run general cleanup of old pending users
  try {
    console.log('Running general cleanup of pending users...');
    const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/cleanup/pending-users`, {
      method: 'POST'
    });

    if (response.ok) {
      const result = await response.json();
      console.log('Cleanup result:', result);
    }
  } catch (error) {
    console.error('Error running general cleanup:', error);
  }
}

function getTierFromPriceId(priceId: string | undefined): string {
  if (!priceId) return 'free';

  switch (priceId) {
    case STRIPE_PRICE_IDS.FREE:
      return 'free';
    case STRIPE_PRICE_IDS.STARTER:
      return 'starter';
    case STRIPE_PRICE_IDS.PROFESSIONAL:
      return 'professional';
    case STRIPE_PRICE_IDS.ENTERPRISE:
      return 'enterprise';
    default:
      console.warn(`Unknown price ID: ${priceId}, defaulting to free`);
      return 'free';
  }
}
