{"/api/admin/cleanup-semantic-cache/route": "app/api/admin/cleanup-semantic-cache/route.js", "/api/admin/verify-user-tier/route": "app/api/admin/verify-user-tier/route.js", "/api/activity/route": "app/api/activity/route.js", "/api/admin/sync-stripe-subscription/route": "app/api/admin/sync-stripe-subscription/route.js", "/api/analytics/cache/route": "app/api/analytics/cache/route.js", "/api/admin/populate-cost-tiers/route": "app/api/admin/populate-cost-tiers/route.js", "/api/analytics/metadata/route": "app/api/analytics/metadata/route.js", "/api/analytics/errors/route": "app/api/analytics/errors/route.js", "/api/analytics/summary/route": "app/api/analytics/summary/route.js", "/api/auth/free-signup/route": "app/api/auth/free-signup/route.js", "/api/analytics/users/route": "app/api/analytics/users/route.js", "/api/auth/check-pending-payment/route": "app/api/auth/check-pending-payment/route.js", "/api/auth/paid-signup/route": "app/api/auth/paid-signup/route.js", "/api/auth/generate-magic-link/route": "app/api/auth/generate-magic-link/route.js", "/api/cache/invalidate/route": "app/api/cache/invalidate/route.js", "/api/chat/conversations/route": "app/api/chat/conversations/route.js", "/api/chat/messages/route": "app/api/chat/messages/route.js", "/api/chat/messages/update-by-timestamp/route": "app/api/chat/messages/update-by-timestamp/route.js", "/api/custom-configs/[configId]/browsing/route": "app/api/custom-configs/[configId]/browsing/route.js", "/api/chat/messages/delete-after-timestamp/route": "app/api/chat/messages/delete-after-timestamp/route.js", "/api/cleanup/pending-users/route": "app/api/cleanup/pending-users/route.js", "/api/custom-configs/[configId]/routing/route": "app/api/custom-configs/[configId]/routing/route.js", "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]/route": "app/api/custom-configs/[configId]/default-key-handler/[apiKeyId]/route.js", "/api/custom-configs/[configId]/default-chat-key/route": "app/api/custom-configs/[configId]/default-chat-key/route.js", "/api/custom-configs/[configId]/route": "app/api/custom-configs/[configId]/route.js", "/api/debug/checkout/route": "app/api/debug/checkout/route.js", "/api/debug/check-stripe-subscription/route": "app/api/debug/check-stripe-subscription/route.js", "/api/custom-configs/route": "app/api/custom-configs/route.js", "/api/debug/auth-test/route": "app/api/debug/auth-test/route.js", "/api/debug/env-check/route": "app/api/debug/env-check/route.js", "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments/route": "app/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments/route.js", "/api/debug/subscription-data/route": "app/api/debug/subscription-data/route.js", "/api/debug/clear-cache/route": "app/api/debug/clear-cache/route.js", "/api/debug/supabase-test/route": "app/api/debug/supabase-test/route.js", "/api/debug/test-webhook/route": "app/api/debug/test-webhook/route.js", "/api/debug/webhook-logs/route": "app/api/debug/webhook-logs/route.js", "/api/debug/workflow-tools/route": "app/api/debug/workflow-tools/route.js", "/api/documents/[documentId]/route": "app/api/documents/[documentId]/route.js", "/api/documents/list/route": "app/api/documents/list/route.js", "/api/documents/search/route": "app/api/documents/search/route.js", "/api/documents/upload/route": "app/api/documents/upload/route.js", "/api/email/test-welcome/route": "app/api/email/test-welcome/route.js", "/api/external/v1/async/result/[jobId]/route": "app/api/external/v1/async/result/[jobId]/route.js", "/api/external/v1/async/submit/route": "app/api/external/v1/async/submit/route.js", "/api/external/v1/async/status/[jobId]/route": "app/api/external/v1/async/status/[jobId]/route.js", "/api/internal/async/process/route": "app/api/internal/async/process/route.js", "/api/keys/[apiKeyId]/roles/[roleName]/route": "app/api/keys/[apiKeyId]/roles/[roleName]/route.js", "/api/internal/classify-multi-role/route": "app/api/internal/classify-multi-role/route.js", "/api/keys/[apiKeyId]/route": "app/api/keys/[apiKeyId]/route.js", "/api/keys/[apiKeyId]/roles/route": "app/api/keys/[apiKeyId]/roles/route.js", "/api/keys/route": "app/api/keys/route.js", "/api/logs/route": "app/api/logs/route.js", "/api/orchestration/classify-roles/route": "app/api/orchestration/classify-roles/route.js", "/api/memory/stats/route": "app/api/memory/stats/route.js", "/api/orchestration/process-step/route": "app/api/orchestration/process-step/route.js", "/api/orchestration/status/[executionId]/route": "app/api/orchestration/status/[executionId]/route.js", "/api/orchestration/start/route": "app/api/orchestration/start/route.js", "/api/orchestration/stream/[executionId]/route": "app/api/orchestration/stream/[executionId]/route.js", "/api/orchestration/synthesis-fallback/[executionId]/route": "app/api/orchestration/synthesis-fallback/[executionId]/route.js", "/api/orchestration/synthesis-stream/[executionId]/route": "app/api/orchestration/synthesis-stream/[executionId]/route.js", "/api/orchestration/synthesis-stream-direct/[executionId]/route": "app/api/orchestration/synthesis-stream-direct/[executionId]/route.js", "/api/playground/route": "app/api/playground/route.js", "/api/providers/list-models/route": "app/api/providers/list-models/route.js", "/api/pricing/tiers/route": "app/api/pricing/tiers/route.js", "/api/quality-analytics/route": "app/api/quality-analytics/route.js", "/api/stripe/payment-success/route": "app/api/stripe/payment-success/route.js", "/api/stripe/price-ids/route": "app/api/stripe/price-ids/route.js", "/api/stripe/subscription-status/route": "app/api/stripe/subscription-status/route.js", "/api/quality-feedback/route": "app/api/quality-feedback/route.js", "/api/stripe/create-checkout-session/route": "app/api/stripe/create-checkout-session/route.js", "/api/stripe/customer-portal/route": "app/api/stripe/customer-portal/route.js", "/api/system-status/route": "app/api/system-status/route.js", "/api/test-subscription/route": "app/api/test-subscription/route.js", "/api/training/jobs/route": "app/api/training/jobs/route.js", "/api/training/jobs/upsert/route": "app/api/training/jobs/upsert/route.js", "/api/test/semantic-cache/route": "app/api/test/semantic-cache/route.js", "/api/user-api-keys/[keyId]/route": "app/api/user-api-keys/[keyId]/route.js", "/api/user-api-keys/route": "app/api/user-api-keys/route.js", "/api/stripe/webhooks/route": "app/api/stripe/webhooks/route.js", "/api/user/subscription-tier/route": "app/api/user/subscription-tier/route.js", "/api/user/sync-subscription/route": "app/api/user/sync-subscription/route.js", "/api/user/custom-roles/[customRoleId]/route": "app/api/user/custom-roles/[customRoleId]/route.js", "/api/user/custom-roles/route": "app/api/user/custom-roles/route.js", "/api/user/delete-account/route": "app/api/user/delete-account/route.js", "/apple-icon.png/route": "app/apple-icon.png/route.js", "/auth/callback/route": "app/auth/callback/route.js", "/api/v1/chat/completions/route": "app/api/v1/chat/completions/route.js", "/icon.png/route": "app/icon.png/route.js", "/_not-found/page": "app/_not-found/page.js", "/add-keys/page": "app/add-keys/page.js", "/analytics/page": "app/analytics/page.js", "/auth/recover/page": "app/auth/recover/page.js", "/billing/page": "app/billing/page.js", "/auth/reset-password/page": "app/auth/reset-password/page.js", "/auth/signup/page": "app/auth/signup/page.js", "/auth/verify-email/page": "app/auth/verify-email/page.js", "/auth/signin/page": "app/auth/signin/page.js", "/dashboard/page": "app/dashboard/page.js", "/checkout/page": "app/checkout/page.js", "/debug-session/page": "app/debug-session/page.js", "/my-models/page": "app/my-models/page.js", "/page": "app/page.js", "/logs/page": "app/logs/page.js", "/my-models/[configId]/page": "app/my-models/[configId]/page.js", "/playground/workflows/page": "app/playground/workflows/page.js", "/pricing/page": "app/pricing/page.js", "/playground/page": "app/playground/page.js", "/routing-setup/[configId]/page": "app/routing-setup/[configId]/page.js", "/routing-setup/page": "app/routing-setup/page.js", "/success/page": "app/success/page.js", "/test-full-browsing/page": "app/test-full-browsing/page.js", "/training/page": "app/training/page.js", "/(app)/settings/page": "app/(app)/settings/page.js", "/about-developer/page": "app/about-developer/page.js", "/blog/build-ai-powered-saas/page": "app/blog/build-ai-powered-saas/page.js", "/blog/building-ai-apps-byok-advantage-2025/page": "app/blog/building-ai-apps-byok-advantage-2025/page.js", "/blog/bootstrap-lean-startup-2025/page": "app/blog/bootstrap-lean-startup-2025/page.js", "/blog/ai-api-gateway-2025-guide/page": "app/blog/ai-api-gateway-2025-guide/page.js", "/blog/ai-model-selection-guide/page": "app/blog/ai-model-selection-guide/page.js", "/blog/cost-effective-ai-development/page": "app/blog/cost-effective-ai-development/page.js", "/blog/roukey-ai-routing-strategies/page": "app/blog/roukey-ai-routing-strategies/page.js", "/blog/roukey-vs-openrouter-vs-portkey-2025/page": "app/blog/roukey-vs-openrouter-vs-portkey-2025/page.js", "/blog/openai-vs-claude-vs-gemini-2025/page": "app/blog/openai-vs-claude-vs-gemini-2025/page.js", "/blog/page": "app/blog/page.js", "/contact/page": "app/contact/page.js", "/cookies/page": "app/cookies/page.js", "/features/page": "app/features/page.js", "/privacy/page": "app/privacy/page.js", "/about/page": "app/about/page.js", "/security/page": "app/security/page.js", "/routing-strategies/page": "app/routing-strategies/page.js", "/terms/page": "app/terms/page.js", "/docs/page": "app/docs/page.js", "/api/external/v1/configs/[configId]/keys/route": "app/api/external/v1/configs/[configId]/keys/route.js", "/api/external/v1/api-keys/[keyId]/route": "app/api/external/v1/api-keys/[keyId]/route.js", "/api/external/v1/chat/completions/route": "app/api/external/v1/chat/completions/route.js", "/api/external/v1/api-keys/route": "app/api/external/v1/api-keys/route.js", "/api/external/v1/models/route": "app/api/external/v1/models/route.js", "/api/external/v1/configs/[configId]/keys/[keyId]/route": "app/api/external/v1/configs/[configId]/keys/[keyId]/route.js", "/api/external/v1/configs/[configId]/route": "app/api/external/v1/configs/[configId]/route.js", "/api/external/v1/configs/route": "app/api/external/v1/configs/route.js", "/api/external/v1/docs/route": "app/api/external/v1/docs/route.js", "/api/external/v1/usage/route": "app/api/external/v1/usage/route.js", "/api/external/v1/providers/route": "app/api/external/v1/providers/route.js", "/api/external/v1/configs/[configId]/routing/route": "app/api/external/v1/configs/[configId]/routing/route.js"}