(()=>{var e={};e.id=5926,e.ids=[5926],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74228:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>S,routeModule:()=>_,serverHooks:()=>d,workAsyncStorage:()=>R,workUnitAsyncStorage:()=>I});var t={};s.r(t),s.d(t,{GET:()=>a});var i=s(96559),p=s(48088),n=s(37719),o=s(32190),c=s(64745),u=s(94473);let E=new c.A(u.Lj.secretKey,{apiVersion:"2025-02-24.acacia"});async function a(e){try{let{searchParams:r}=new URL(e.url),s=r.get("subscription_id");if(!s)return o.NextResponse.json({error:"subscription_id parameter required"},{status:400});let t=await E.subscriptions.retrieve(s),i=t.items.data[0]?.price.id,p=function(e){if(!e)return"free";switch(e){case u.Dm.FREE:return"free";case u.Dm.STARTER:return"starter";case u.Dm.PROFESSIONAL:return"professional";case u.Dm.ENTERPRISE:return"enterprise";default:return"free"}}(i);return o.NextResponse.json({subscription:{id:t.id,status:t.status,customer:t.customer,priceId:i,tier:p,current_period_start:t.current_period_start,current_period_end:t.current_period_end,cancel_at_period_end:t.cancel_at_period_end},priceIdMapping:{expected_starter:u.Dm.STARTER,expected_professional:u.Dm.PROFESSIONAL,actual:i,mapped_tier:p}})}catch(e){return o.NextResponse.json({error:"Failed to check subscription"},{status:500})}}let _=new i.AppRouteRouteModule({definition:{kind:p.RouteKind.APP_ROUTE,page:"/api/debug/check-stripe-subscription/route",pathname:"/api/debug/check-stripe-subscription",filename:"route",bundlePath:"app/api/debug/check-stripe-subscription/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\debug\\check-stripe-subscription\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:R,workUnitAsyncStorage:I,serverHooks:d}=_;function S(){return(0,n.patchFetch)({workAsyncStorage:R,workUnitAsyncStorage:I})}},78335:()=>{},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},94473:(e,r,s)=>{"use strict";s.d(r,{Dm:()=>i,Lj:()=>t,Zu:()=>p});let t={publishableKey:process.env.STRIPE_LIVE_PUBLISHABLE_KEY,secretKey:process.env.STRIPE_LIVE_SECRET_KEY,webhookSecret:process.env.STRIPE_LIVE_WEBHOOK_SECRET},i={FREE:process.env.STRIPE_LIVE_FREE_PRICE_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRICE_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID},p={FREE:process.env.STRIPE_LIVE_FREE_PRODUCT_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRODUCT_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID};process.env.STRIPE_LIVE_FREE_PRICE_ID,process.env.STRIPE_LIVE_STARTER_PRICE_ID,process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID,t.publishableKey&&t.publishableKey.substring(0,20),t.secretKey&&t.secretKey.substring(0,20),t.webhookSecret&&t.webhookSecret.substring(0,15)},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,580,4745],()=>s(74228));module.exports=t})();