import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { STRIPE_KEYS, STRIPE_PRICE_IDS } from '@/lib/stripe-config';

const stripe = new Stripe(STRIPE_KEYS.secretKey, {
  apiVersion: '2025-02-24.acacia',
});

function getTierFromPriceId(priceId: string | undefined): string {
  if (!priceId) return 'free';

  switch (priceId) {
    case STRIPE_PRICE_IDS.FREE:
      return 'free';
    case STRIPE_PRICE_IDS.STARTER:
      return 'starter';
    case STRIPE_PRICE_IDS.PROFESSIONAL:
      return 'professional';
    case STRIPE_PRICE_IDS.ENTERPRISE:
      return 'enterprise';
    default:
      console.warn(`Unknown price ID: ${priceId}, defaulting to free`);
      return 'free';
  }
}

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const subscriptionId = searchParams.get('subscription_id');
    
    if (!subscriptionId) {
      return NextResponse.json({ error: 'subscription_id parameter required' }, { status: 400 });
    }

    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    const priceId = subscription.items.data[0]?.price.id;
    const tier = getTierFromPriceId(priceId);

    return NextResponse.json({
      subscription: {
        id: subscription.id,
        status: subscription.status,
        customer: subscription.customer,
        priceId: priceId,
        tier: tier,
        current_period_start: subscription.current_period_start,
        current_period_end: subscription.current_period_end,
        cancel_at_period_end: subscription.cancel_at_period_end
      },
      priceIdMapping: {
        expected_starter: STRIPE_PRICE_IDS.STARTER,
        expected_professional: STRIPE_PRICE_IDS.PROFESSIONAL,
        actual: priceId,
        mapped_tier: tier
      }
    });
  } catch (error) {
    console.error('Error checking Stripe subscription:', error);
    return NextResponse.json({ error: 'Failed to check subscription' }, { status: 500 });
  }
}
