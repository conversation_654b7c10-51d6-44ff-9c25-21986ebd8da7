(()=>{var e={};e.id=7726,e.ids=[7726],e.modules={2502:e=>{"use strict";e.exports=import("prettier/plugins/html")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},39756:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>_,serverHooks:()=>f,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{POST:()=>l});var i=t(96559),a=t(48088),u=t(37719),o=t(32190),n=t(39398),p=t(64745);let c=(0,n.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY),d=new p.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-02-24.acacia"});async function l(e){try{let{email:r,password:s,fullName:i}=await e.json();if(!r||!s)return o.NextResponse.json({error:"Email and password are required"},{status:400});let{data:a,error:u}=await c.auth.admin.createUser({email:r,password:s,email_confirm:!0,user_metadata:{full_name:i||""}});if(u)return o.NextResponse.json({error:u.message},{status:400});if(!a.user)return o.NextResponse.json({error:"Failed to create user"},{status:500});let{error:n}=await c.from("user_profiles").insert({id:a.user.id,full_name:i||"",subscription_tier:"free",subscription_status:"active",user_status:"active",created_at:new Date().toISOString(),updated_at:new Date().toISOString()}),p=null,l=null;try{p=(await d.customers.create({email:a.user.email,metadata:{supabase_user_id:a.user.id,tier:"free"}})).id,l=(await d.subscriptions.create({customer:p,items:[{price:process.env.STRIPE_FREE_PRICE_ID}],metadata:{supabase_user_id:a.user.id,tier:"free"}})).id}catch(e){}let{error:_}=await c.from("subscriptions").insert({user_id:a.user.id,tier:"free",status:"active",is_free_tier:!0,stripe_subscription_id:l,stripe_customer_id:p,current_period_start:new Date().toISOString(),current_period_end:new Date(Date.now()+31536e6).toISOString(),cancel_at_period_end:!1,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}),x=new Date().toISOString().slice(0,7),{error:m}=await c.from("workflow_usage").insert({user_id:a.user.id,month_year:x,executions_count:0,active_workflows_count:0,created_at:new Date().toISOString(),updated_at:new Date().toISOString()});try{let{sendWelcomeEmail:e}=await t.e(146).then(t.bind(t,10146));await e({userEmail:a.user.email,userName:i||"New User",userTier:"free"})}catch(e){}return o.NextResponse.json({success:!0,user:{id:a.user.id,email:a.user.email,tier:"free"},message:"Free account created successfully"})}catch(e){return o.NextResponse.json({error:"Internal server error"},{status:500})}}let _=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/auth/free-signup/route",pathname:"/api/auth/free-signup",filename:"route",bundlePath:"app/api/auth/free-signup/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\auth\\free-signup\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:m,serverHooks:f}=_;function w(){return(0,u.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:m})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83505:e=>{"use strict";e.exports=import("prettier/standalone")},84297:e=>{"use strict";e.exports=require("async_hooks")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,9398,4745],()=>t(39756));module.exports=s})();