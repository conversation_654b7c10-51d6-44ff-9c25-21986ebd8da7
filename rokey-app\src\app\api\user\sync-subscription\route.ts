import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/service-role';
import { syncUserWithStripe, getUserTierFromStripe } from '@/lib/stripe-sync';

export async function POST(req: NextRequest) {
  try {
    const { email, userId } = await req.json();
    
    let targetUserId = userId;
    
    // If email provided but no userId, look up the user
    if (email && !userId) {
      const supabase = createServiceRoleClient();
      const { data: authUser, error: authError } = await supabase
        .from('auth.users')
        .select('id, email')
        .eq('email', email)
        .single();

      if (authError || !authUser) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }
      
      targetUserId = authUser.id;
    }

    if (!targetUserId) {
      return NextResponse.json({ error: 'User ID or email required' }, { status: 400 });
    }

    console.log(`🔄 Manual sync requested for user: ${targetUserId}`);

    // Get current tier from Stripe
    const stripeTier = await getUserTierFromStripe(targetUserId);
    
    // Sync database with Stripe
    const syncResult = await syncUserWithStripe(targetUserId);

    return NextResponse.json({
      success: syncResult.success,
      userId: targetUserId,
      stripeTier: stripeTier,
      syncResult: syncResult,
      message: syncResult.success 
        ? `Successfully synced user to tier: ${syncResult.tier}`
        : `Sync failed: ${syncResult.error}`
    });

  } catch (error) {
    console.error('Error in sync endpoint:', error);
    return NextResponse.json({ 
      error: 'Failed to sync subscription',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const email = searchParams.get('email');
    const userId = searchParams.get('userId');
    
    let targetUserId = userId;
    
    // If email provided but no userId, look up the user
    if (email && !userId) {
      const supabase = createServiceRoleClient();
      const { data: authUser, error: authError } = await supabase
        .from('auth.users')
        .select('id, email')
        .eq('email', email)
        .single();

      if (authError || !authUser) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }
      
      targetUserId = authUser.id;
    }

    if (!targetUserId) {
      return NextResponse.json({ error: 'User ID or email required' }, { status: 400 });
    }

    // Just get the current tier from Stripe without syncing
    const stripeTier = await getUserTierFromStripe(targetUserId);

    return NextResponse.json({
      userId: targetUserId,
      stripeTier: stripeTier,
      message: `Current Stripe tier: ${stripeTier}`
    });

  } catch (error) {
    console.error('Error checking tier:', error);
    return NextResponse.json({ 
      error: 'Failed to check tier',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
