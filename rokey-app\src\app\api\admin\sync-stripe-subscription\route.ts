import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(req: NextRequest) {
  try {
    const { email, tier } = await req.json();

    if (!email || !tier) {
      return NextResponse.json({ error: 'Email and tier are required' }, { status: 400 });
    }

    if (!['free', 'starter', 'professional', 'enterprise'].includes(tier)) {
      return NextResponse.json({ error: 'Invalid tier' }, { status: 400 });
    }

    console.log(`Starting manual sync for user: ${email} to tier: ${tier}`);

    // 1. Find user in auth.users
    const { data: authUser, error: authError } = await supabase
      .from('auth.users')
      .select('id, email')
      .eq('email', email)
      .single();

    if (authError || !authUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    console.log(`Found user: ${authUser.id}`);

    // 2. Get their current subscription from database
    const { data: dbSubscription, error: dbError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', authUser.id)
      .single();

    if (dbError || !dbSubscription) {
      return NextResponse.json({ error: 'Database subscription not found' }, { status: 404 });
    }

    console.log(`Found DB subscription - current tier: ${dbSubscription.tier}, updating to: ${tier}`);

    // 3. Update database subscription to the specified tier
    const { error: updateSubError } = await supabase
      .from('subscriptions')
      .update({
        tier: tier,
        is_free_tier: tier === 'free',
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', authUser.id);

    if (updateSubError) {
      console.error('Error updating subscription:', updateSubError);
      return NextResponse.json({ error: 'Failed to update subscription' }, { status: 500 });
    }

    // 4. Update user profile to match
    const { error: updateProfileError } = await supabase
      .from('user_profiles')
      .update({
        subscription_tier: tier,
        user_status: 'active',
        updated_at: new Date().toISOString(),
      })
      .eq('id', authUser.id);

    if (updateProfileError) {
      console.error('Error updating user profile:', updateProfileError);
      return NextResponse.json({ error: 'Failed to update user profile' }, { status: 500 });
    }

    // 5. Update auth user metadata
    const { error: metadataError } = await supabase.auth.admin.updateUserById(authUser.id, {
      user_metadata: {
        payment_status: tier === 'free' ? 'free' : 'completed',
        plan: tier
      }
    });

    if (metadataError) {
      console.error('Error updating user metadata:', metadataError);
    }

    console.log(`✅ Successfully synced user ${email} to tier: ${tier}`);

    return NextResponse.json({
      success: true,
      user: {
        id: authUser.id,
        email: authUser.email
      },
      before: {
        db_tier: dbSubscription.tier,
        db_status: dbSubscription.status,
        db_is_free_tier: dbSubscription.is_free_tier
      },
      after: {
        new_tier: tier,
        is_free_tier: tier === 'free'
      }
    });

  } catch (error) {
    console.error('Error syncing subscription:', error);
    return NextResponse.json({ 
      error: 'Failed to sync subscription',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
