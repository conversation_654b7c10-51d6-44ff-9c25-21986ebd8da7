import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/service-role';
import <PERSON><PERSON> from 'stripe';
import { STRIPE_KEYS } from '@/lib/stripe-config';

const stripe = new Stripe(STRIPE_KEYS.secretKey, {
  apiVersion: '2025-02-24.acacia',
});

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const sessionId = searchParams.get('session_id');
    const plan = searchParams.get('plan');

    if (!sessionId) {
      console.error('No session ID provided');
      return NextResponse.redirect(new URL('/pricing?error=no_session', req.url));
    }

    console.log('Processing payment success for session:', sessionId);

    // Retrieve the checkout session from Stripe
    const session = await stripe.checkout.sessions.retrieve(sessionId);
    
    if (!session) {
      console.error('Session not found:', sessionId);
      return NextResponse.redirect(new URL('/pricing?error=session_not_found', req.url));
    }

    // Get user ID from session metadata
    const userId = session.metadata?.user_id;
    
    if (!userId || userId === 'pending_signup') {
      console.error('No user ID in session metadata');
      return NextResponse.redirect(new URL('/pricing?error=no_user_id', req.url));
    }

    console.log('Found user ID in session:', userId);

    const supabase = createServiceRoleClient();

    // Check if user is now active (webhook should have processed by now)
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('user_status, subscription_status, subscription_tier')
      .eq('id', userId)
      .single();

    if (profileError) {
      console.error('Error fetching user profile:', profileError);
      return NextResponse.redirect(new URL('/pricing?error=profile_error', req.url));
    }

    if (!profile) {
      console.error('User profile not found for user:', userId);
      return NextResponse.redirect(new URL('/pricing?error=profile_not_found', req.url));
    }

    console.log('User profile status:', profile);

    // If user is not yet active, wait a moment for webhook processing
    if (profile.user_status !== 'active') {
      console.log('User not yet active, checking again in a moment...');
      
      // Wait 2 seconds and check again
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const { data: updatedProfile } = await supabase
        .from('user_profiles')
        .select('user_status, subscription_status, subscription_tier')
        .eq('id', userId)
        .single();

      if (updatedProfile?.user_status !== 'active' || updatedProfile?.subscription_tier === 'free') {
        console.log('User not properly upgraded after wait, but payment succeeded - upgrading manually');

        // Get the subscription details from Stripe to determine the correct tier
        let targetTier = plan || 'professional'; // fallback to professional

        try {
          if (session.subscription) {
            const stripeSubscription = await stripe.subscriptions.retrieve(session.subscription as string);
            const priceId = stripeSubscription.items.data[0]?.price.id;

            // Determine tier from price ID
            if (priceId?.includes('starter') || priceId?.includes('1RcekMC97XFBBUvdYnl0leWM')) {
              targetTier = 'starter';
            } else if (priceId?.includes('professional') || priceId?.includes('1RcelCC97XFBBUvdfvuJnGnC')) {
              targetTier = 'professional';
            } else if (priceId?.includes('enterprise') || priceId?.includes('1RceljC97XFBBUvdyCtcBYyT')) {
              targetTier = 'enterprise';
            }

            console.log('Determined tier from Stripe subscription:', targetTier, 'Price ID:', priceId);
          }
        } catch (stripeError) {
          console.error('Error fetching Stripe subscription for manual upgrade:', stripeError);
        }

        // Manually activate the user and update subscription tier (webhook might be delayed)
        const { error: updateError } = await supabase
          .from('user_profiles')
          .update({
            user_status: 'active',
            subscription_status: 'active',
            subscription_tier: targetTier,
            updated_at: new Date().toISOString(),
          })
          .eq('id', userId);

        if (updateError) {
          console.error('Error manually activating user:', updateError);
        } else {
          console.log('User manually activated and upgraded after payment success');

          // Also update the subscription record if we have Stripe subscription details
          if (session.subscription) {
            try {
              const stripeSubscription = await stripe.subscriptions.retrieve(session.subscription as string);

              const { error: subscriptionError } = await supabase
                .from('subscriptions')
                .upsert({
                  user_id: userId,
                  stripe_subscription_id: stripeSubscription.id,
                  stripe_customer_id: stripeSubscription.customer as string,
                  tier: targetTier,
                  status: stripeSubscription.status,
                  current_period_start: new Date(stripeSubscription.current_period_start * 1000).toISOString(),
                  current_period_end: new Date(stripeSubscription.current_period_end * 1000).toISOString(),
                  cancel_at_period_end: stripeSubscription.cancel_at_period_end,
                  is_free_tier: targetTier === 'free',
                  updated_at: new Date().toISOString(),
                }, {
                  onConflict: 'user_id'
                });

              if (subscriptionError) {
                console.error('Error manually updating subscription record:', subscriptionError);
              } else {
                console.log('Subscription record manually updated for user:', userId);
              }
            } catch (subscriptionUpdateError) {
              console.error('Error manually updating subscription:', subscriptionUpdateError);
            }
          }

          // Send welcome email when manually activating user
          console.log('📧 Sending welcome email to manually activated user:', userId);
          try {
            const { data: authUser } = await supabase.auth.admin.getUserById(userId);

            if (authUser.user?.email) {
              const { sendWelcomeEmail } = await import('@/lib/email/welcomeEmail');
              const emailSent = await sendWelcomeEmail({
                userEmail: authUser.user.email,
                userName: authUser.user.user_metadata?.full_name || 'New User',
                userTier: targetTier
              });

              if (emailSent) {
                console.log('✅ Welcome email sent successfully to:', authUser.user.email);
              } else {
                console.log('⚠️ Welcome email failed to send to:', authUser.user.email);
              }
            }
          } catch (emailError) {
            console.error('❌ Error sending welcome email:', emailError);
            // Don't fail the payment success if email fails
          }
        }
      }
    }

    // Get user details for auto sign-in
    const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);

    if (userError || !userData.user) {
      console.error('Error fetching user for session creation:', userError);
      return NextResponse.redirect(new URL(`/success?session_id=${sessionId}&plan=${plan}&manual_signin=true`, req.url));
    }

    console.log('User found, redirecting to success page with auto sign-in');

    // Redirect to success page with user details for auto sign-in
    const successUrl = new URL('/success', req.url);
    successUrl.searchParams.set('session_id', sessionId);
    successUrl.searchParams.set('plan', plan || '');
    successUrl.searchParams.set('user_id', userId);
    successUrl.searchParams.set('email', userData.user.email!);
    successUrl.searchParams.set('auto_signin', 'true');

    return NextResponse.redirect(successUrl);

  } catch (error) {
    console.error('Error in payment success handler:', error);
    return NextResponse.redirect(new URL('/pricing?error=payment_success_error', req.url));
  }
}
