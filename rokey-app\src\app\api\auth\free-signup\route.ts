import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import Stripe from 'stripe';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-02-24.acacia',
});

export async function POST(req: NextRequest) {
  try {
    const { email, password, fullName } = await req.json();

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Create user account
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Auto-confirm for free tier
      user_metadata: {
        full_name: fullName || '',
      },
    });

    if (authError) {
      console.error('Error creating user:', authError);
      return NextResponse.json(
        { error: authError.message },
        { status: 400 }
      );
    }

    if (!authData.user) {
      return NextResponse.json(
        { error: 'Failed to create user' },
        { status: 500 }
      );
    }

    // Create user profile with free tier
    const { error: profileError } = await supabase
      .from('user_profiles')
      .insert({
        id: authData.user.id,
        full_name: fullName || '',
        subscription_tier: 'free',
        subscription_status: 'active',
        user_status: 'active', // Free users are immediately active
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

    if (profileError) {
      console.error('Error creating user profile:', profileError);
      // Don't fail the signup if profile creation fails, we can retry later
    }

    // Create Stripe customer and subscribe them to the free plan
    let stripeCustomerId: string | null = null;
    let stripeSubscriptionId: string | null = null;
    try {
      console.log('Creating Stripe customer for free user:', authData.user.email);
      const stripeCustomer = await stripe.customers.create({
        email: authData.user.email!,
        metadata: {
          supabase_user_id: authData.user.id,
          tier: 'free',
        },
      });
      stripeCustomerId = stripeCustomer.id;
      console.log('✅ Stripe customer created for free user:', stripeCustomerId);

      // Subscribe them to the free plan
      console.log('Creating Stripe subscription for free plan...');
      const freeSubscription = await stripe.subscriptions.create({
        customer: stripeCustomerId,
        items: [
          {
            price: process.env.STRIPE_FREE_PRICE_ID!, // Your free plan price ID
          },
        ],
        metadata: {
          supabase_user_id: authData.user.id,
          tier: 'free',
        },
      });
      stripeSubscriptionId = freeSubscription.id;
      console.log('✅ Stripe free subscription created:', stripeSubscriptionId);
    } catch (stripeError) {
      console.error('❌ Error creating Stripe customer/subscription for free user:', stripeError);
      // Don't fail the signup if Stripe creation fails, we can create it later
    }

    // Create a free tier "subscription" record for tracking
    const { error: subscriptionError } = await supabase
      .from('subscriptions')
      .insert({
        user_id: authData.user.id,
        tier: 'free',
        status: 'active',
        is_free_tier: true,
        stripe_subscription_id: stripeSubscriptionId, // Store the Stripe subscription ID
        stripe_customer_id: stripeCustomerId, // Store the Stripe customer ID
        current_period_start: new Date().toISOString(),
        current_period_end: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year from now
        cancel_at_period_end: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

    if (subscriptionError) {
      console.error('Error creating free subscription record:', subscriptionError);
      // Don't fail the signup if subscription record creation fails
    }

    // Initialize workflow usage tracking for current month
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format
    const { error: usageError } = await supabase
      .from('workflow_usage')
      .insert({
        user_id: authData.user.id,
        month_year: currentMonth,
        executions_count: 0,
        active_workflows_count: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

    if (usageError) {
      console.error('Error creating workflow usage record:', usageError);
      // Don't fail the signup if usage tracking creation fails
    }

    // Send welcome email immediately for free users (they're active right away)
    console.log('📧 Sending welcome email to new free user:', authData.user.email);
    try {
      const { sendWelcomeEmail } = await import('@/lib/email/welcomeEmail');
      const emailSent = await sendWelcomeEmail({
        userEmail: authData.user.email!,
        userName: fullName || 'New User',
        userTier: 'free'
      });

      if (emailSent) {
        console.log('✅ Welcome email sent successfully to:', authData.user.email);
      } else {
        console.log('⚠️ Welcome email failed to send to:', authData.user.email);
      }
    } catch (emailError) {
      console.error('❌ Error sending welcome email:', emailError);
      // Don't fail the signup if email fails
    }

    return NextResponse.json({
      success: true,
      user: {
        id: authData.user.id,
        email: authData.user.email,
        tier: 'free',
      },
      message: 'Free account created successfully',
    });

  } catch (error) {
    console.error('Error in free signup:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
