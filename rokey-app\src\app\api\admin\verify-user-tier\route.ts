import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const email = searchParams.get('email');
    
    if (!email) {
      return NextResponse.json({ error: 'Email parameter required' }, { status: 400 });
    }

    // Find user in auth.users
    const { data: authUser, error: authError } = await supabase
      .from('auth.users')
      .select('id, email')
      .eq('email', email)
      .single();

    if (authError || !authUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', authUser.id)
      .single();

    // Get subscription
    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', authUser.id)
      .single();

    return NextResponse.json({
      user: {
        id: authUser.id,
        email: authUser.email
      },
      profile: profile || null,
      subscription: subscription || null,
      errors: {
        profile: profileError?.message || null,
        subscription: subError?.message || null
      }
    });

  } catch (error) {
    console.error('Error verifying user tier:', error);
    return NextResponse.json({ 
      error: 'Failed to verify user tier',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
