import Stripe from 'stripe';
import { createClient } from '@supabase/supabase-js';
import { STRIPE_KEYS, STRIPE_PRICE_IDS } from './stripe-config';

const stripe = new Stripe(STRIPE_KEYS.secretKey, {
  apiVersion: '2025-02-24.acacia',
});

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

function getTierFromPriceId(priceId: string | undefined): string {
  if (!priceId) return 'free';

  switch (priceId) {
    case STRIPE_PRICE_IDS.FREE:
      return 'free';
    case STRIPE_PRICE_IDS.STARTER:
      return 'starter';
    case STRIPE_PRICE_IDS.PROFESSIONAL:
      return 'professional';
    case STRIPE_PRICE_IDS.ENTERPRISE:
      return 'enterprise';
    default:
      console.warn(`Unknown price ID: ${priceId}, defaulting to free`);
      return 'free';
  }
}

/**
 * Get user's ACTUAL tier from Stripe (not database)
 * This is the source of truth
 */
export async function getUserTierFromStripe(userId: string): Promise<string> {
  try {
    // Get user's subscription from database to get Stripe subscription ID
    const { data: subscription, error } = await supabase
      .from('subscriptions')
      .select('stripe_subscription_id, stripe_customer_id')
      .eq('user_id', userId)
      .single();

    if (error || !subscription) {
      console.log(`No subscription found for user ${userId}, defaulting to free`);
      return 'free';
    }

    // If no Stripe subscription ID, they're free tier
    if (!subscription.stripe_subscription_id) {
      return 'free';
    }

    // Fetch the actual subscription from Stripe
    const stripeSubscription = await stripe.subscriptions.retrieve(subscription.stripe_subscription_id);
    
    // Check if subscription is active
    if (stripeSubscription.status !== 'active') {
      console.log(`Stripe subscription ${subscription.stripe_subscription_id} is not active: ${stripeSubscription.status}`);
      return 'free';
    }

    // Get the price ID and determine tier
    const priceId = stripeSubscription.items.data[0]?.price.id;
    const tier = getTierFromPriceId(priceId);

    console.log(`✅ User ${userId} actual Stripe tier: ${tier} (price: ${priceId})`);
    return tier;

  } catch (error) {
    console.error(`Error fetching Stripe tier for user ${userId}:`, error);
    return 'free'; // Fallback to free on error
  }
}

/**
 * Sync database with Stripe for a specific user
 * This updates the database to match what's in Stripe
 */
export async function syncUserWithStripe(userId: string): Promise<{ success: boolean; tier: string; error?: string }> {
  try {
    console.log(`🔄 Syncing user ${userId} with Stripe...`);

    // Get actual tier from Stripe
    const actualTier = await getUserTierFromStripe(userId);

    // Get current database subscription
    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (subError || !subscription) {
      return { success: false, tier: 'free', error: 'No subscription found in database' };
    }

    // If Stripe subscription exists, get full details for database update
    let updateData: any = {
      tier: actualTier,
      is_free_tier: actualTier === 'free',
      updated_at: new Date().toISOString(),
    };

    if (subscription.stripe_subscription_id && actualTier !== 'free') {
      try {
        const stripeSubscription = await stripe.subscriptions.retrieve(subscription.stripe_subscription_id);
        updateData = {
          ...updateData,
          status: stripeSubscription.status,
          current_period_start: new Date(stripeSubscription.current_period_start * 1000).toISOString(),
          current_period_end: new Date(stripeSubscription.current_period_end * 1000).toISOString(),
          cancel_at_period_end: stripeSubscription.cancel_at_period_end,
        };
      } catch (stripeError) {
        console.error('Error fetching Stripe subscription details:', stripeError);
      }
    }

    // Update subscription table
    const { error: updateSubError } = await supabase
      .from('subscriptions')
      .update(updateData)
      .eq('user_id', userId);

    if (updateSubError) {
      console.error('Error updating subscription:', updateSubError);
      return { success: false, tier: actualTier, error: 'Failed to update subscription' };
    }

    // Update user profile
    const { error: updateProfileError } = await supabase
      .from('user_profiles')
      .update({
        subscription_tier: actualTier,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (updateProfileError) {
      console.error('Error updating user profile:', updateProfileError);
      return { success: false, tier: actualTier, error: 'Failed to update user profile' };
    }

    console.log(`✅ Successfully synced user ${userId} to tier: ${actualTier}`);
    return { success: true, tier: actualTier };

  } catch (error) {
    console.error(`Error syncing user ${userId} with Stripe:`, error);
    return { success: false, tier: 'free', error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Get user tier with automatic sync if database is outdated
 * This is the function to use throughout the app
 */
export async function getUserTierWithSync(userId: string): Promise<string> {
  try {
    // First, get what's in the database
    const { data: profile, error } = await supabase
      .from('user_profiles')
      .select('subscription_tier, updated_at')
      .eq('id', userId)
      .single();

    if (error || !profile) {
      console.log(`No profile found for user ${userId}, checking Stripe directly`);
      const tier = await getUserTierFromStripe(userId);
      // Try to sync in background
      syncUserWithStripe(userId).catch(err => console.error('Background sync failed:', err));
      return tier;
    }

    // Check if database was updated recently (within last 5 minutes)
    const lastUpdated = new Date(profile.updated_at);
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    
    if (lastUpdated > fiveMinutesAgo) {
      // Database is recent, trust it
      return profile.subscription_tier;
    }

    // Database might be stale, check Stripe
    console.log(`Database tier for user ${userId} might be stale, checking Stripe...`);
    const actualTier = await getUserTierFromStripe(userId);
    
    // If tiers don't match, sync in background
    if (actualTier !== profile.subscription_tier) {
      console.log(`Tier mismatch for user ${userId}: DB=${profile.subscription_tier}, Stripe=${actualTier}. Syncing...`);
      syncUserWithStripe(userId).catch(err => console.error('Background sync failed:', err));
    }

    return actualTier;

  } catch (error) {
    console.error(`Error getting tier for user ${userId}:`, error);
    return 'free';
  }
}
