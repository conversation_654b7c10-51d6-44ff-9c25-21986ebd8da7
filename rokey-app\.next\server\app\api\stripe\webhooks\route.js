(()=>{var e={};e.id=7158,e.ids=[7158],e.modules={2502:e=>{"use strict";e.exports=import("prettier/plugins/html")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},41259:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>g,serverHooks:()=>y,workAsyncStorage:()=>P,workUnitAsyncStorage:()=>T});var s={};r.r(s),r.d(s,{GET:()=>l,POST:()=>E});var i=r(96559),a=r(48088),o=r(37719),n=r(32190),u=r(64745),c=r(39398),p=r(94473);let d=new u.A(p.Lj.secretKey,{apiVersion:"2025-02-24.acacia"}),_=(0,c.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function l(){return new Response("Stripe webhook endpoint is active",{status:200,headers:{"Content-Type":"text/plain"}})}async function E(e){let t,r=await e.text(),s=e.headers.get("stripe-signature");if(!s)return n.NextResponse.json({error:"Missing signature"},{status:400});try{t=d.webhooks.constructEvent(r,s,p.Lj.webhookSecret)}catch(e){return n.NextResponse.json({error:"Invalid signature"},{status:400})}try{await fetch(`${process.env.NEXT_PUBLIC_APP_URL||"http://localhost:3000"}/api/debug/webhook-logs`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({eventType:t.type,eventId:t.id,details:{created:t.created,livemode:t.livemode,object:t.data.object}})}).catch(e=>console.log("Failed to log webhook event:",e.message))}catch(e){}try{switch(t.type){case"checkout.session.completed":await m(t.data.object);break;case"customer.subscription.created":await I(t.data.object);break;case"customer.subscription.updated":await S(t.data.object);break;case"customer.subscription.deleted":await f(t.data.object);break;case"invoice.payment_succeeded":await b(t.data.object);break;case"invoice.payment_failed":await R(t.data.object);break;case"checkout.session.expired":await w(t.data.object)}return n.NextResponse.json({received:!0})}catch(e){return n.NextResponse.json({error:"Webhook processing failed"},{status:500})}}async function m(e){let t,s=!e.subscription&&0===e.amount_total;if(!e.customer&&!s)return;let i=null;if(e.subscription)i=await d.subscriptions.retrieve(e.subscription);else if(!s)return;let a=e.metadata?.signup==="true",o=e.metadata?.user_id;if(a&&e.metadata?.pending_user_data)try{let t=JSON.parse(e.metadata.pending_user_data),{data:r,error:s}=await _.auth.admin.createUser({email:t.email,password:t.password,email_confirm:!0,user_metadata:{full_name:`${t.firstName} ${t.lastName}`,first_name:t.firstName,last_name:t.lastName,selected_plan:t.plan}});if(s)throw s;o=r.user.id}catch(e){throw e}if(!o)return;if(i)t=h(i.items.data[0]?.price.id);else{if(!s)return;t=e.metadata?.tier||"starter"}let n={user_id:o,tier:t,updated_at:new Date().toISOString()};if(i)n={...n,stripe_subscription_id:i.id,stripe_customer_id:i.customer,status:i.status,current_period_start:new Date(1e3*i.current_period_start).toISOString(),current_period_end:new Date(1e3*i.current_period_end).toISOString(),cancel_at_period_end:i.cancel_at_period_end,is_free_tier:!1};else if(s){let t=new Date,r=new Date(t.getTime()+31536e6);n={...n,stripe_subscription_id:null,stripe_customer_id:e.customer||null,status:"active",current_period_start:t.toISOString(),current_period_end:r.toISOString(),cancel_at_period_end:!1,is_free_tier:!1}}let{error:u}=await _.from("subscriptions").upsert(n,{onConflict:"user_id"});if(u)throw u;let{error:c}=await _.from("user_profiles").upsert({id:o,subscription_tier:t,subscription_status:"active",user_status:"active",updated_at:new Date().toISOString()});if(c);else try{let{data:e}=await _.auth.admin.getUserById(o);if(e.user?.email){let{sendWelcomeEmail:s}=await r.e(146).then(r.bind(r,10146));await s({userEmail:e.user.email,userName:e.user.user_metadata?.full_name||"New User",userTier:t})}}catch(e){}if(o){let{error:e}=await _.auth.admin.updateUserById(o,{user_metadata:{payment_status:"completed",plan:t}})}}async function I(e){}async function S(e){let t=e.items.data[0]?.price.id,r=h(t);try{await fetch(`${process.env.NEXT_PUBLIC_APP_URL||"http://localhost:3000"}/api/debug/webhook-logs`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({eventType:"customer.subscription.updated",subscriptionId:e.id,customerId:e.customer,priceId:t,tier:r,status:e.status,details:{currentPeriodStart:e.current_period_start,currentPeriodEnd:e.current_period_end,cancelAtPeriodEnd:e.cancel_at_period_end}})}).catch(e=>console.log("Failed to log subscription update:",e.message))}catch(e){}let{error:s}=await _.from("subscriptions").update({tier:r,status:e.status,current_period_start:new Date(1e3*e.current_period_start).toISOString(),current_period_end:new Date(1e3*e.current_period_end).toISOString(),cancel_at_period_end:e.cancel_at_period_end,is_free_tier:"free"===r,updated_at:new Date().toISOString()}).eq("stripe_subscription_id",e.id);if(s)throw s;let{data:i,error:a}=await _.from("subscriptions").select("user_id").eq("stripe_subscription_id",e.id).single();if(!a&&i){let{error:e}=await _.from("user_profiles").update({subscription_tier:r,updated_at:new Date().toISOString()}).eq("id",i.user_id);if(e);else{let{data:e,error:t}=await _.from("user_profiles").select("subscription_tier").eq("id",i.user_id).single()}}}async function f(e){let{error:t}=await _.from("subscriptions").update({status:"canceled",updated_at:new Date().toISOString()}).eq("stripe_subscription_id",e.id);if(t)throw t;let{data:r}=await _.from("subscriptions").select("user_id").eq("stripe_subscription_id",e.id).single();r&&await _.from("user_profiles").update({subscription_tier:"starter"}).eq("id",r.user_id)}async function b(e){if(e.subscription){let{error:t}=await _.from("subscriptions").update({status:"active",updated_at:new Date().toISOString()}).eq("stripe_subscription_id",e.subscription)}}async function R(e){if(e.subscription){let{error:t}=await _.from("subscriptions").update({status:"past_due",updated_at:new Date().toISOString()}).eq("stripe_subscription_id",e.subscription)}}async function w(e){let t=e.metadata?.user_id;if(e.metadata?.signup,t&&"pending_signup"!==t)try{let{data:e,error:r}=await _.auth.admin.getUserById(t);if(r)return;if(e?.user){let r=e.user.user_metadata?.payment_status;if("pending"===r){let{error:e}=await _.auth.admin.deleteUser(t)}}}catch(e){}try{let e=await fetch("https://roukey.online/api/cleanup/pending-users",{method:"POST"});e.ok&&await e.json()}catch(e){}}function h(e){if(!e)return"free";switch(e){case p.Dm.FREE:return"free";case p.Dm.STARTER:return"starter";case p.Dm.PROFESSIONAL:return"professional";case p.Dm.ENTERPRISE:return"enterprise";default:return"free"}}let g=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/stripe/webhooks/route",pathname:"/api/stripe/webhooks",filename:"route",bundlePath:"app/api/stripe/webhooks/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\stripe\\webhooks\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:P,workUnitAsyncStorage:T,serverHooks:y}=g;function v(){return(0,o.patchFetch)({workAsyncStorage:P,workUnitAsyncStorage:T})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83505:e=>{"use strict";e.exports=import("prettier/standalone")},84297:e=>{"use strict";e.exports=require("async_hooks")},91645:e=>{"use strict";e.exports=require("net")},94473:(e,t,r)=>{"use strict";r.d(t,{Dm:()=>i,Lj:()=>s,Zu:()=>a});let s={publishableKey:process.env.STRIPE_LIVE_PUBLISHABLE_KEY,secretKey:process.env.STRIPE_LIVE_SECRET_KEY,webhookSecret:process.env.STRIPE_LIVE_WEBHOOK_SECRET},i={FREE:process.env.STRIPE_LIVE_FREE_PRICE_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRICE_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID},a={FREE:process.env.STRIPE_LIVE_FREE_PRODUCT_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRODUCT_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID};process.env.STRIPE_LIVE_FREE_PRICE_ID,process.env.STRIPE_LIVE_STARTER_PRICE_ID,process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID,s.publishableKey&&s.publishableKey.substring(0,20),s.secretKey&&s.secretKey.substring(0,20),s.webhookSecret&&s.webhookSecret.substring(0,15)},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,4745],()=>r(41259));module.exports=s})();