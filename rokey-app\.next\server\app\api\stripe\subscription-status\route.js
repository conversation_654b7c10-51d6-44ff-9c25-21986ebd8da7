(()=>{var e={};e.id=6782,e.ids=[6782],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},25282:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>C,routeModule:()=>I,serverHooks:()=>m,workAsyncStorage:()=>S,workUnitAsyncStorage:()=>P});var t={};s.r(t),s.d(t,{GET:()=>R,POST:()=>f});var n=s(96559),i=s(48088),a=s(37719),o=s(32190),c=s(39398),u=s(94473),p=s(64745);let d=(0,c.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY),E=new p.A(u.Lj.secretKey,{apiVersion:"2025-02-24.acacia"});async function _(e){try{let{data:r,error:s}=await d.from("subscriptions").select("stripe_subscription_id").eq("user_id",e).single();if(s||!r||!r.stripe_subscription_id)return{tier:"free"};let t=await E.subscriptions.retrieve(r.stripe_subscription_id);if("active"!==t.status)return{tier:"free"};let n=t.items.data[0]?.price.id,i=function(e){if(!e)return"free";switch(e){case u.Dm.FREE:return"free";case u.Dm.STARTER:return"starter";case u.Dm.PROFESSIONAL:return"professional";case u.Dm.ENTERPRISE:return"enterprise";default:return"free"}}(n);return l(e,i,t).catch(e=>console.error("Background database update failed:",e)),{tier:i,subscription:t}}catch(e){return{tier:"free"}}}async function l(e,r,s){try{await d.from("subscriptions").update({tier:r,status:s.status,current_period_start:new Date(1e3*s.current_period_start).toISOString(),current_period_end:new Date(1e3*s.current_period_end).toISOString(),cancel_at_period_end:s.cancel_at_period_end,is_free_tier:"free"===r,updated_at:new Date().toISOString()}).eq("user_id",e),await d.from("user_profiles").update({subscription_tier:r,updated_at:new Date().toISOString()}).eq("id",e)}catch(e){}}async function R(e){try{let{searchParams:r}=new URL(e.url),s=r.get("userId");if(!s)return o.NextResponse.json({error:"Missing userId parameter"},{status:400});if("true"===process.env.SUPABASE_SERVER_CONNECTIVITY_ISSUE)return o.NextResponse.json({hasActiveSubscription:!0,tier:"free",status:"active",currentPeriodEnd:null,cancelAtPeriodEnd:!1,isFree:!0,fallback:!0,message:"Using fallback due to network connectivity issues"});let{tier:t,subscription:n}=await _(s),{data:i}=await d.from("subscriptions").select("*").eq("user_id",s).single();if(!n)return o.NextResponse.json({hasActiveSubscription:!1,tier:"free",status:null,currentPeriodEnd:null,cancelAtPeriodEnd:!1,isFree:!0,source:"stripe"});{let e="active"===n.status,r=new Date(1e3*n.current_period_end)<new Date;return o.NextResponse.json({hasActiveSubscription:e&&!r,tier:t,status:n.status,currentPeriodEnd:n.current_period_end,currentPeriodStart:n.current_period_start,cancelAtPeriodEnd:n.cancel_at_period_end,stripeCustomerId:n.customer,stripeSubscriptionId:n.id,isFree:"free"===t,source:"stripe"})}}catch(e){if(e instanceof Error&&(e.message.includes("fetch failed")||e.message.includes("network")))return o.NextResponse.json({hasActiveSubscription:!0,tier:"free",status:"active",currentPeriodEnd:null,cancelAtPeriodEnd:!1,isFree:!0,fallback:!0,message:"Using fallback due to network error"});return o.NextResponse.json({error:"Internal server error"},{status:500})}}async function f(e){try{let{userId:r}=await e.json();if(!r)return o.NextResponse.json({error:"Missing userId"},{status:400});if("true"===process.env.SUPABASE_SERVER_CONNECTIVITY_ISSUE)return o.NextResponse.json({tier:"free",usage:{configurations:0,apiKeys:0,apiRequests:0},limits:g("free"),canCreateConfig:!0,canCreateApiKey:!0,fallback:!0,message:"Using fallback due to network connectivity issues"});let s=new Date().toISOString().slice(0,7),{data:t,error:n}=await d.from("usage_tracking").select("*").eq("user_id",r).eq("month_year",s).single(),{tier:i}=await _(r),{count:a}=await d.from("custom_api_configs").select("*",{count:"exact",head:!0}).eq("user_id",r),{count:c}=await d.from("api_keys").select("*",{count:"exact",head:!0}).eq("user_id",r),u=g(i);return o.NextResponse.json({tier:i,usage:{configurations:a||0,apiKeys:c||0,apiRequests:t?.api_requests_count||0},limits:u,canCreateConfig:(a||0)<u.configurations,canCreateApiKey:(c||0)<u.apiKeysPerConfig})}catch(e){if(e instanceof Error&&(e.message.includes("fetch failed")||e.message.includes("network")))return o.NextResponse.json({tier:"free",usage:{configurations:0,apiKeys:0,apiRequests:0},limits:g("free"),canCreateConfig:!0,canCreateApiKey:!0,fallback:!0,message:"Using fallback due to network error"});return o.NextResponse.json({error:"Internal server error"},{status:500})}}function g(e){switch(e){case"free":default:return{configurations:1,apiKeysPerConfig:3,apiRequests:999999,canUseAdvancedRouting:!1,canUseCustomRoles:!1,maxCustomRoles:0,canUsePromptEngineering:!1,canUseKnowledgeBase:!1,knowledgeBaseDocuments:0,canUseSemanticCaching:!1};case"starter":return{configurations:15,apiKeysPerConfig:5,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:3,canUsePromptEngineering:!0,canUseKnowledgeBase:!1,knowledgeBaseDocuments:0,canUseSemanticCaching:!1};case"professional":return{configurations:999999,apiKeysPerConfig:999999,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:999999,canUsePromptEngineering:!0,canUseKnowledgeBase:!0,knowledgeBaseDocuments:5,canUseSemanticCaching:!0};case"enterprise":return{configurations:999999,apiKeysPerConfig:999999,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:999999,canUsePromptEngineering:!0,canUseKnowledgeBase:!0,knowledgeBaseDocuments:999999,canUseSemanticCaching:!0}}}let I=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/stripe/subscription-status/route",pathname:"/api/stripe/subscription-status",filename:"route",bundlePath:"app/api/stripe/subscription-status/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\stripe\\subscription-status\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:S,workUnitAsyncStorage:P,serverHooks:m}=I;function C(){return(0,a.patchFetch)({workAsyncStorage:S,workUnitAsyncStorage:P})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94473:(e,r,s)=>{"use strict";s.d(r,{Dm:()=>n,Lj:()=>t,Zu:()=>i});let t={publishableKey:process.env.STRIPE_LIVE_PUBLISHABLE_KEY,secretKey:process.env.STRIPE_LIVE_SECRET_KEY,webhookSecret:process.env.STRIPE_LIVE_WEBHOOK_SECRET},n={FREE:process.env.STRIPE_LIVE_FREE_PRICE_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRICE_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID},i={FREE:process.env.STRIPE_LIVE_FREE_PRODUCT_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRODUCT_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID};process.env.STRIPE_LIVE_FREE_PRICE_ID,process.env.STRIPE_LIVE_STARTER_PRICE_ID,process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID,t.publishableKey&&t.publishableKey.substring(0,20),t.secretKey&&t.secretKey.substring(0,20),t.webhookSecret&&t.webhookSecret.substring(0,15)},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,580,9398,4745],()=>s(25282));module.exports=t})();