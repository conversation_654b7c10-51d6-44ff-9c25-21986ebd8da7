/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stripe/subscription-status/route";
exports.ids = ["app/api/stripe/subscription-status/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fsubscription-status%2Froute&page=%2Fapi%2Fstripe%2Fsubscription-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fsubscription-status%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fsubscription-status%2Froute&page=%2Fapi%2Fstripe%2Fsubscription-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fsubscription-status%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_stripe_subscription_status_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/stripe/subscription-status/route.ts */ \"(rsc)/./src/app/api/stripe/subscription-status/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stripe/subscription-status/route\",\n        pathname: \"/api/stripe/subscription-status\",\n        filename: \"route\",\n        bundlePath: \"app/api/stripe/subscription-status/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\stripe\\\\subscription-status\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_stripe_subscription_status_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fsubscription-status%2Froute&page=%2Fapi%2Fstripe%2Fsubscription-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fsubscription-status%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/stripe/subscription-status/route.ts":
/*!*********************************************************!*\
  !*** ./src/app/api/stripe/subscription-status/route.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/stripe-config */ \"(rsc)/./src/lib/stripe-config.ts\");\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n\n\n\n\nconst supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_2__[\"default\"](_lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__.STRIPE_KEYS.secretKey, {\n    apiVersion: '2025-02-24.acacia'\n});\nfunction getTierFromPriceId(priceId) {\n    if (!priceId) return 'free';\n    switch(priceId){\n        case _lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__.STRIPE_PRICE_IDS.FREE:\n            return 'free';\n        case _lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__.STRIPE_PRICE_IDS.STARTER:\n            return 'starter';\n        case _lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__.STRIPE_PRICE_IDS.PROFESSIONAL:\n            return 'professional';\n        case _lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__.STRIPE_PRICE_IDS.ENTERPRISE:\n            return 'enterprise';\n        default:\n            console.warn(`Unknown price ID: ${priceId}, defaulting to free`);\n            return 'free';\n    }\n}\nasync function getUserTierFromStripe(userId) {\n    try {\n        // Get user's subscription from database to get Stripe subscription ID\n        const { data: subscription, error } = await supabase.from('subscriptions').select('stripe_subscription_id').eq('user_id', userId).single();\n        if (error || !subscription || !subscription.stripe_subscription_id) {\n            console.log(`No Stripe subscription found for user ${userId}, defaulting to free`);\n            return {\n                tier: 'free'\n            };\n        }\n        // Fetch the actual subscription from Stripe\n        const stripeSubscription = await stripe.subscriptions.retrieve(subscription.stripe_subscription_id);\n        // Check if subscription is active\n        if (stripeSubscription.status !== 'active') {\n            console.log(`Stripe subscription ${subscription.stripe_subscription_id} is not active: ${stripeSubscription.status}`);\n            return {\n                tier: 'free'\n            };\n        }\n        // Get the price ID and determine tier\n        const priceId = stripeSubscription.items.data[0]?.price.id;\n        const tier = getTierFromPriceId(priceId);\n        console.log(`✅ User ${userId} actual Stripe tier: ${tier} (price: ${priceId})`);\n        // Update database in background to keep it in sync\n        updateDatabaseTier(userId, tier, stripeSubscription).catch((err)=>console.error('Background database update failed:', err));\n        return {\n            tier,\n            subscription: stripeSubscription\n        };\n    } catch (error) {\n        console.error(`Error fetching Stripe tier for user ${userId}:`, error);\n        return {\n            tier: 'free'\n        }; // Fallback to free on error\n    }\n}\nasync function updateDatabaseTier(userId, tier, stripeSubscription) {\n    try {\n        // Update subscription table\n        await supabase.from('subscriptions').update({\n            tier,\n            status: stripeSubscription.status,\n            current_period_start: new Date(stripeSubscription.current_period_start * 1000).toISOString(),\n            current_period_end: new Date(stripeSubscription.current_period_end * 1000).toISOString(),\n            cancel_at_period_end: stripeSubscription.cancel_at_period_end,\n            is_free_tier: tier === 'free',\n            updated_at: new Date().toISOString()\n        }).eq('user_id', userId);\n        // Update user profile\n        await supabase.from('user_profiles').update({\n            subscription_tier: tier,\n            updated_at: new Date().toISOString()\n        }).eq('id', userId);\n        console.log(`📝 Database updated for user ${userId} to tier: ${tier}`);\n    } catch (error) {\n        console.error('Error updating database:', error);\n    }\n}\nasync function GET(req) {\n    try {\n        const { searchParams } = new URL(req.url);\n        const userId = searchParams.get('userId');\n        console.log('🔍 Subscription status API called for user:', userId);\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing userId parameter'\n            }, {\n                status: 400\n            });\n        }\n        // Check for network connectivity issues\n        if (process.env.SUPABASE_SERVER_CONNECTIVITY_ISSUE === 'true') {\n            console.log('Network connectivity issue detected, returning fallback response');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                hasActiveSubscription: true,\n                tier: 'free',\n                status: 'active',\n                currentPeriodEnd: null,\n                cancelAtPeriodEnd: false,\n                isFree: true,\n                fallback: true,\n                message: 'Using fallback due to network connectivity issues'\n            });\n        }\n        // Get tier directly from Stripe (source of truth)\n        console.log(`🔄 Getting tier for user ${userId} from Stripe...`);\n        const { tier, subscription: stripeSubscription } = await getUserTierFromStripe(userId);\n        // Get database subscription for additional details\n        const { data: dbSubscription } = await supabase.from('subscriptions').select('*').eq('user_id', userId).single();\n        console.log(`✅ Stripe tier: ${tier} for user ${userId}`);\n        // If user has a Stripe subscription, use those details\n        if (stripeSubscription) {\n            const isActive = stripeSubscription.status === 'active';\n            const currentPeriodEnd = new Date(stripeSubscription.current_period_end * 1000);\n            const isExpired = currentPeriodEnd < new Date();\n            console.log('GET subscription-status - Stripe subscription found:', {\n                userId,\n                tier,\n                status: stripeSubscription.status,\n                isActive,\n                isExpired,\n                hasActiveSubscription: isActive && !isExpired\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                hasActiveSubscription: isActive && !isExpired,\n                tier,\n                status: stripeSubscription.status,\n                currentPeriodEnd: stripeSubscription.current_period_end,\n                currentPeriodStart: stripeSubscription.current_period_start,\n                cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,\n                stripeCustomerId: stripeSubscription.customer,\n                stripeSubscriptionId: stripeSubscription.id,\n                isFree: tier === 'free',\n                source: 'stripe' // Indicate this came from Stripe\n            });\n        } else {\n            // Free tier user\n            console.log('GET subscription-status - Free tier user:', {\n                userId,\n                tier\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                hasActiveSubscription: false,\n                tier: 'free',\n                status: null,\n                currentPeriodEnd: null,\n                cancelAtPeriodEnd: false,\n                isFree: true,\n                source: 'stripe' // Still checked Stripe, just no subscription\n            });\n        }\n    } catch (error) {\n        console.error('Error fetching subscription status:', error);\n        // If there's a network error, return fallback response\n        if (error instanceof Error && (error.message.includes('fetch failed') || error.message.includes('network'))) {\n            console.log('Network error detected, returning fallback response');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                hasActiveSubscription: true,\n                tier: 'free',\n                status: 'active',\n                currentPeriodEnd: null,\n                cancelAtPeriodEnd: false,\n                isFree: true,\n                fallback: true,\n                message: 'Using fallback due to network error'\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(req) {\n    try {\n        const { userId } = await req.json();\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing userId'\n            }, {\n                status: 400\n            });\n        }\n        // Check for network connectivity issues\n        if (process.env.SUPABASE_SERVER_CONNECTIVITY_ISSUE === 'true') {\n            console.log('Network connectivity issue detected, returning fallback usage response');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                tier: 'free',\n                usage: {\n                    configurations: 0,\n                    apiKeys: 0,\n                    apiRequests: 0\n                },\n                limits: getTierLimits('free'),\n                canCreateConfig: true,\n                canCreateApiKey: true,\n                fallback: true,\n                message: 'Using fallback due to network connectivity issues'\n            });\n        }\n        // Get current usage for the user\n        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format\n        const { data: usage, error: usageError } = await supabase.from('usage_tracking').select('*').eq('user_id', userId).eq('month_year', currentMonth).single();\n        // Get tier directly from Stripe (source of truth)\n        console.log(`🔄 Getting tier for user ${userId} from Stripe for usage check...`);\n        const { tier } = await getUserTierFromStripe(userId);\n        console.log('POST subscription-status - User tier from Stripe:', {\n            userId,\n            stripeTier: tier,\n            source: 'stripe'\n        });\n        // Get configuration and API key counts\n        const { count: configCount } = await supabase.from('custom_api_configs').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId);\n        const { count: apiKeyCount } = await supabase.from('api_keys').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId);\n        // Note: Workflow features will be added in future updates\n        // Calculate tier limits\n        const limits = getTierLimits(tier);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            tier,\n            usage: {\n                configurations: configCount || 0,\n                apiKeys: apiKeyCount || 0,\n                apiRequests: usage?.api_requests_count || 0\n            },\n            limits,\n            canCreateConfig: (configCount || 0) < limits.configurations,\n            canCreateApiKey: (apiKeyCount || 0) < limits.apiKeysPerConfig\n        });\n    } catch (error) {\n        console.error('Error fetching usage status:', error);\n        // If there's a network error, return fallback response\n        if (error instanceof Error && (error.message.includes('fetch failed') || error.message.includes('network'))) {\n            console.log('Network error detected, returning fallback usage response');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                tier: 'free',\n                usage: {\n                    configurations: 0,\n                    apiKeys: 0,\n                    apiRequests: 0\n                },\n                limits: getTierLimits('free'),\n                canCreateConfig: true,\n                canCreateApiKey: true,\n                fallback: true,\n                message: 'Using fallback due to network error'\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nfunction getTierLimits(tier) {\n    switch(tier){\n        case 'free':\n            return {\n                configurations: 1,\n                apiKeysPerConfig: 3,\n                apiRequests: 999999,\n                canUseAdvancedRouting: false,\n                canUseCustomRoles: false,\n                maxCustomRoles: 0,\n                canUsePromptEngineering: false,\n                canUseKnowledgeBase: false,\n                knowledgeBaseDocuments: 0,\n                canUseSemanticCaching: false\n            };\n        case 'starter':\n            return {\n                configurations: 15,\n                apiKeysPerConfig: 5,\n                apiRequests: 999999,\n                canUseAdvancedRouting: true,\n                canUseCustomRoles: true,\n                maxCustomRoles: 3,\n                canUsePromptEngineering: true,\n                canUseKnowledgeBase: false,\n                knowledgeBaseDocuments: 0,\n                canUseSemanticCaching: false\n            };\n        case 'professional':\n            return {\n                configurations: 999999,\n                apiKeysPerConfig: 999999,\n                apiRequests: 999999,\n                canUseAdvancedRouting: true,\n                canUseCustomRoles: true,\n                maxCustomRoles: 999999,\n                canUsePromptEngineering: true,\n                canUseKnowledgeBase: true,\n                knowledgeBaseDocuments: 5,\n                canUseSemanticCaching: true\n            };\n        case 'enterprise':\n            return {\n                configurations: 999999,\n                apiKeysPerConfig: 999999,\n                apiRequests: 999999,\n                canUseAdvancedRouting: true,\n                canUseCustomRoles: true,\n                maxCustomRoles: 999999,\n                canUsePromptEngineering: true,\n                canUseKnowledgeBase: true,\n                knowledgeBaseDocuments: 999999,\n                canUseSemanticCaching: true\n            };\n        default:\n            return {\n                configurations: 1,\n                apiKeysPerConfig: 3,\n                apiRequests: 999999,\n                canUseAdvancedRouting: false,\n                canUseCustomRoles: false,\n                maxCustomRoles: 0,\n                canUsePromptEngineering: false,\n                canUseKnowledgeBase: false,\n                knowledgeBaseDocuments: 0,\n                canUseSemanticCaching: false\n            };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/stripe/subscription-status/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-config.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe-config.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PUBLIC_STRIPE_PRICE_IDS: () => (/* binding */ PUBLIC_STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_ENV_INFO: () => (/* binding */ STRIPE_ENV_INFO),\n/* harmony export */   STRIPE_KEYS: () => (/* binding */ STRIPE_KEYS),\n/* harmony export */   STRIPE_PRICE_IDS: () => (/* binding */ STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_PRODUCT_IDS: () => (/* binding */ STRIPE_PRODUCT_IDS)\n/* harmony export */ });\n// Stripe Configuration with Environment Detection\n// Automatically switches between test and live keys based on environment\nconst isProduction = \"development\" === 'production';\n// Stripe Keys - Auto-selected based on environment\nconst STRIPE_KEYS = {\n    publishableKey: isProduction ? process.env.STRIPE_LIVE_PUBLISHABLE_KEY : process.env.STRIPE_TEST_PUBLISHABLE_KEY,\n    secretKey: isProduction ? process.env.STRIPE_LIVE_SECRET_KEY : process.env.STRIPE_TEST_SECRET_KEY,\n    webhookSecret: isProduction ? process.env.STRIPE_LIVE_WEBHOOK_SECRET : process.env.STRIPE_TEST_WEBHOOK_SECRET\n};\n// Stripe Price IDs - Auto-selected based on environment\nconst STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Stripe Product IDs - Auto-selected based on environment\nconst STRIPE_PRODUCT_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRODUCT_ID : process.env.STRIPE_TEST_FREE_PRODUCT_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRODUCT_ID : process.env.STRIPE_TEST_STARTER_PRODUCT_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRODUCT_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID : process.env.STRIPE_TEST_ENTERPRISE_PRODUCT_ID\n};\n// Public Price IDs for frontend (auto-selected based on environment)\nconst PUBLIC_STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Environment info for debugging\nconst STRIPE_ENV_INFO = {\n    isProduction,\n    environment: isProduction ? 'LIVE' : 'TEST',\n    keysUsed: {\n        publishable: STRIPE_KEYS.publishableKey ? STRIPE_KEYS.publishableKey.substring(0, 20) + '...' : 'undefined',\n        secret: STRIPE_KEYS.secretKey ? STRIPE_KEYS.secretKey.substring(0, 20) + '...' : 'undefined',\n        webhook: STRIPE_KEYS.webhookSecret ? STRIPE_KEYS.webhookSecret.substring(0, 15) + '...' : 'undefined'\n    }\n};\n// Log environment info in development\nif (!isProduction) {\n    console.log('🔧 Stripe Environment:', STRIPE_ENV_INFO.environment);\n    console.log('🔑 Using keys:', STRIPE_ENV_INFO.keysUsed);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-config.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/stripe","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/qs","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/object-inspect","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/hasown","vendor-chunks/get-intrinsic","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/call-bound"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fsubscription-status%2Froute&page=%2Fapi%2Fstripe%2Fsubscription-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fsubscription-status%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();