{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "EV-23L5bk2unzKDWl1SjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "th7/Bqhp3ppPf+zXJNzzVAz/bf54dfvNUdHhBvqAyiY=", "__NEXT_PREVIEW_MODE_ID": "913f3264dec31bf23fbb854ba721eef3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d27b436560de4b81ee9f92b47a8d2921183f98217c732f8f81b1897aea163f6b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5d13da0fa7deedacb1edd6a7de41630b0b4db8a773ed1a6c4da481c2474a4f78"}}}, "functions": {"/api/external/v1/configs/[configId]/keys/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/configs/[configId]/keys/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/configs/[configId]/keys/route.js"], "name": "app/api/external/v1/configs/[configId]/keys/route", "page": "/api/external/v1/configs/[configId]/keys/route", "matchers": [{"regexp": "^/api/external/v1/configs/(?<configId>[^/]+?)/keys$", "originalSource": "/api/external/v1/configs/[configId]/keys"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "EV-23L5bk2unzKDWl1SjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "th7/Bqhp3ppPf+zXJNzzVAz/bf54dfvNUdHhBvqAyiY=", "__NEXT_PREVIEW_MODE_ID": "913f3264dec31bf23fbb854ba721eef3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d27b436560de4b81ee9f92b47a8d2921183f98217c732f8f81b1897aea163f6b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5d13da0fa7deedacb1edd6a7de41630b0b4db8a773ed1a6c4da481c2474a4f78"}}, "/api/external/v1/api-keys/[keyId]/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/api-keys/[keyId]/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/api-keys/[keyId]/route.js"], "name": "app/api/external/v1/api-keys/[keyId]/route", "page": "/api/external/v1/api-keys/[keyId]/route", "matchers": [{"regexp": "^/api/external/v1/api\\-keys/(?<keyId>[^/]+?)$", "originalSource": "/api/external/v1/api-keys/[keyId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "EV-23L5bk2unzKDWl1SjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "th7/Bqhp3ppPf+zXJNzzVAz/bf54dfvNUdHhBvqAyiY=", "__NEXT_PREVIEW_MODE_ID": "913f3264dec31bf23fbb854ba721eef3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d27b436560de4b81ee9f92b47a8d2921183f98217c732f8f81b1897aea163f6b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5d13da0fa7deedacb1edd6a7de41630b0b4db8a773ed1a6c4da481c2474a4f78"}}, "/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/833.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "EV-23L5bk2unzKDWl1SjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "th7/Bqhp3ppPf+zXJNzzVAz/bf54dfvNUdHhBvqAyiY=", "__NEXT_PREVIEW_MODE_ID": "913f3264dec31bf23fbb854ba721eef3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d27b436560de4b81ee9f92b47a8d2921183f98217c732f8f81b1897aea163f6b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5d13da0fa7deedacb1edd6a7de41630b0b4db8a773ed1a6c4da481c2474a4f78"}}, "/api/external/v1/api-keys/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/api-keys/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/api-keys/route.js"], "name": "app/api/external/v1/api-keys/route", "page": "/api/external/v1/api-keys/route", "matchers": [{"regexp": "^/api/external/v1/api\\-keys$", "originalSource": "/api/external/v1/api-keys"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "EV-23L5bk2unzKDWl1SjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "th7/Bqhp3ppPf+zXJNzzVAz/bf54dfvNUdHhBvqAyiY=", "__NEXT_PREVIEW_MODE_ID": "913f3264dec31bf23fbb854ba721eef3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d27b436560de4b81ee9f92b47a8d2921183f98217c732f8f81b1897aea163f6b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5d13da0fa7deedacb1edd6a7de41630b0b4db8a773ed1a6c4da481c2474a4f78"}}, "/api/external/v1/models/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/models/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/models/route.js"], "name": "app/api/external/v1/models/route", "page": "/api/external/v1/models/route", "matchers": [{"regexp": "^/api/external/v1/models$", "originalSource": "/api/external/v1/models"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "EV-23L5bk2unzKDWl1SjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "th7/Bqhp3ppPf+zXJNzzVAz/bf54dfvNUdHhBvqAyiY=", "__NEXT_PREVIEW_MODE_ID": "913f3264dec31bf23fbb854ba721eef3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d27b436560de4b81ee9f92b47a8d2921183f98217c732f8f81b1897aea163f6b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5d13da0fa7deedacb1edd6a7de41630b0b4db8a773ed1a6c4da481c2474a4f78"}}, "/api/external/v1/configs/[configId]/keys/[keyId]/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/configs/[configId]/keys/[keyId]/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/configs/[configId]/keys/[keyId]/route.js"], "name": "app/api/external/v1/configs/[configId]/keys/[keyId]/route", "page": "/api/external/v1/configs/[configId]/keys/[keyId]/route", "matchers": [{"regexp": "^/api/external/v1/configs/(?<configId>[^/]+?)/keys/(?<keyId>[^/]+?)$", "originalSource": "/api/external/v1/configs/[configId]/keys/[keyId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "EV-23L5bk2unzKDWl1SjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "th7/Bqhp3ppPf+zXJNzzVAz/bf54dfvNUdHhBvqAyiY=", "__NEXT_PREVIEW_MODE_ID": "913f3264dec31bf23fbb854ba721eef3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d27b436560de4b81ee9f92b47a8d2921183f98217c732f8f81b1897aea163f6b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5d13da0fa7deedacb1edd6a7de41630b0b4db8a773ed1a6c4da481c2474a4f78"}}, "/api/external/v1/configs/[configId]/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/configs/[configId]/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/configs/[configId]/route.js"], "name": "app/api/external/v1/configs/[configId]/route", "page": "/api/external/v1/configs/[configId]/route", "matchers": [{"regexp": "^/api/external/v1/configs/(?<configId>[^/]+?)$", "originalSource": "/api/external/v1/configs/[configId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "EV-23L5bk2unzKDWl1SjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "th7/Bqhp3ppPf+zXJNzzVAz/bf54dfvNUdHhBvqAyiY=", "__NEXT_PREVIEW_MODE_ID": "913f3264dec31bf23fbb854ba721eef3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d27b436560de4b81ee9f92b47a8d2921183f98217c732f8f81b1897aea163f6b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5d13da0fa7deedacb1edd6a7de41630b0b4db8a773ed1a6c4da481c2474a4f78"}}, "/api/external/v1/configs/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/configs/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/configs/route.js"], "name": "app/api/external/v1/configs/route", "page": "/api/external/v1/configs/route", "matchers": [{"regexp": "^/api/external/v1/configs$", "originalSource": "/api/external/v1/configs"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "EV-23L5bk2unzKDWl1SjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "th7/Bqhp3ppPf+zXJNzzVAz/bf54dfvNUdHhBvqAyiY=", "__NEXT_PREVIEW_MODE_ID": "913f3264dec31bf23fbb854ba721eef3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d27b436560de4b81ee9f92b47a8d2921183f98217c732f8f81b1897aea163f6b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5d13da0fa7deedacb1edd6a7de41630b0b4db8a773ed1a6c4da481c2474a4f78"}}, "/api/external/v1/docs/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/docs/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/app/api/external/v1/docs/route.js"], "name": "app/api/external/v1/docs/route", "page": "/api/external/v1/docs/route", "matchers": [{"regexp": "^/api/external/v1/docs$", "originalSource": "/api/external/v1/docs"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "EV-23L5bk2unzKDWl1SjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "th7/Bqhp3ppPf+zXJNzzVAz/bf54dfvNUdHhBvqAyiY=", "__NEXT_PREVIEW_MODE_ID": "913f3264dec31bf23fbb854ba721eef3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d27b436560de4b81ee9f92b47a8d2921183f98217c732f8f81b1897aea163f6b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5d13da0fa7deedacb1edd6a7de41630b0b4db8a773ed1a6c4da481c2474a4f78"}}, "/api/external/v1/usage/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/usage/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/usage/route.js"], "name": "app/api/external/v1/usage/route", "page": "/api/external/v1/usage/route", "matchers": [{"regexp": "^/api/external/v1/usage$", "originalSource": "/api/external/v1/usage"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "EV-23L5bk2unzKDWl1SjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "th7/Bqhp3ppPf+zXJNzzVAz/bf54dfvNUdHhBvqAyiY=", "__NEXT_PREVIEW_MODE_ID": "913f3264dec31bf23fbb854ba721eef3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d27b436560de4b81ee9f92b47a8d2921183f98217c732f8f81b1897aea163f6b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5d13da0fa7deedacb1edd6a7de41630b0b4db8a773ed1a6c4da481c2474a4f78"}}, "/api/external/v1/providers/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/providers/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/833.js", "server/app/api/external/v1/providers/route.js"], "name": "app/api/external/v1/providers/route", "page": "/api/external/v1/providers/route", "matchers": [{"regexp": "^/api/external/v1/providers$", "originalSource": "/api/external/v1/providers"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "EV-23L5bk2unzKDWl1SjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "th7/Bqhp3ppPf+zXJNzzVAz/bf54dfvNUdHhBvqAyiY=", "__NEXT_PREVIEW_MODE_ID": "913f3264dec31bf23fbb854ba721eef3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d27b436560de4b81ee9f92b47a8d2921183f98217c732f8f81b1897aea163f6b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5d13da0fa7deedacb1edd6a7de41630b0b4db8a773ed1a6c4da481c2474a4f78"}}, "/api/external/v1/configs/[configId]/routing/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/configs/[configId]/routing/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/configs/[configId]/routing/route.js"], "name": "app/api/external/v1/configs/[configId]/routing/route", "page": "/api/external/v1/configs/[configId]/routing/route", "matchers": [{"regexp": "^/api/external/v1/configs/(?<configId>[^/]+?)/routing$", "originalSource": "/api/external/v1/configs/[configId]/routing"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "EV-23L5bk2unzKDWl1SjK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "th7/Bqhp3ppPf+zXJNzzVAz/bf54dfvNUdHhBvqAyiY=", "__NEXT_PREVIEW_MODE_ID": "913f3264dec31bf23fbb854ba721eef3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d27b436560de4b81ee9f92b47a8d2921183f98217c732f8f81b1897aea163f6b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5d13da0fa7deedacb1edd6a7de41630b0b4db8a773ed1a6c4da481c2474a4f78"}}}, "sortedMiddleware": ["/"]}