(()=>{var e={};e.id=5464,e.ids=[5464],e.modules={2502:e=>{"use strict";e.exports=import("prettier/plugins/html")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},60230:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>S,routeModule:()=>l,serverHooks:()=>I,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>R});var t={};s.r(t),s.d(t,{GET:()=>d});var i=s(96559),n=s(48088),u=s(37719),o=s(32190),a=s(80726),c=s(64745),p=s(94473);let _=new c.A(p.Lj.secretKey,{apiVersion:"2025-02-24.acacia"});async function d(e){try{let{searchParams:r}=new URL(e.url),t=r.get("session_id"),i=r.get("plan");if(!t)return o.NextResponse.redirect(new URL("/pricing?error=no_session",e.url));let n=await _.checkout.sessions.retrieve(t);if(!n)return o.NextResponse.redirect(new URL("/pricing?error=session_not_found",e.url));let u=n.metadata?.user_id;if(!u||"pending_signup"===u)return o.NextResponse.redirect(new URL("/pricing?error=no_user_id",e.url));let c=(0,a.H)(),{data:p,error:d}=await c.from("user_profiles").select("user_status, subscription_status, subscription_tier").eq("id",u).single();if(d)return o.NextResponse.redirect(new URL("/pricing?error=profile_error",e.url));if(!p)return o.NextResponse.redirect(new URL("/pricing?error=profile_not_found",e.url));if("active"!==p.user_status){await new Promise(e=>setTimeout(e,2e3));let{data:e}=await c.from("user_profiles").select("user_status, subscription_status, subscription_tier").eq("id",u).single();if(e?.user_status!=="active"||e?.subscription_tier==="free"){let e=i||"professional";try{if(n.subscription){let r=await _.subscriptions.retrieve(n.subscription),s=r.items.data[0]?.price.id;s?.includes("starter")||s?.includes("1RcekMC97XFBBUvdYnl0leWM")?e="starter":s?.includes("professional")||s?.includes("1RcelCC97XFBBUvdfvuJnGnC")?e="professional":(s?.includes("enterprise")||s?.includes("1RceljC97XFBBUvdyCtcBYyT"))&&(e="enterprise")}}catch(e){}let{error:r}=await c.from("user_profiles").update({user_status:"active",subscription_status:"active",subscription_tier:e,updated_at:new Date().toISOString()}).eq("id",u);if(r);else{if(n.subscription)try{let r=await _.subscriptions.retrieve(n.subscription),{error:s}=await c.from("subscriptions").upsert({user_id:u,stripe_subscription_id:r.id,stripe_customer_id:r.customer,tier:e,status:r.status,current_period_start:new Date(1e3*r.current_period_start).toISOString(),current_period_end:new Date(1e3*r.current_period_end).toISOString(),cancel_at_period_end:r.cancel_at_period_end,is_free_tier:"free"===e,updated_at:new Date().toISOString()},{onConflict:"user_id"})}catch(e){}try{let{data:r}=await c.auth.admin.getUserById(u);if(r.user?.email){let{sendWelcomeEmail:t}=await s.e(146).then(s.bind(s,10146));await t({userEmail:r.user.email,userName:r.user.user_metadata?.full_name||"New User",userTier:e})}}catch(e){}}}}let{data:l,error:E}=await c.auth.admin.getUserById(u);if(E||!l.user)return o.NextResponse.redirect(new URL(`/success?session_id=${t}&plan=${i}&manual_signin=true`,e.url));let R=new URL("/success",e.url);return R.searchParams.set("session_id",t),R.searchParams.set("plan",i||""),R.searchParams.set("user_id",u),R.searchParams.set("email",l.user.email),R.searchParams.set("auto_signin","true"),o.NextResponse.redirect(R)}catch(r){return o.NextResponse.redirect(new URL("/pricing?error=payment_success_error",e.url))}}let l=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/stripe/payment-success/route",pathname:"/api/stripe/payment-success",filename:"route",bundlePath:"app/api/stripe/payment-success/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\stripe\\payment-success\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:E,workUnitAsyncStorage:R,serverHooks:I}=l;function S(){return(0,u.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:R})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80726:(e,r,s)=>{"use strict";s.d(r,{H:()=>i});var t=s(39398);function i(){return(0,t.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}})}},81630:e=>{"use strict";e.exports=require("http")},83505:e=>{"use strict";e.exports=import("prettier/standalone")},84297:e=>{"use strict";e.exports=require("async_hooks")},91645:e=>{"use strict";e.exports=require("net")},94473:(e,r,s)=>{"use strict";s.d(r,{Dm:()=>i,Lj:()=>t,Zu:()=>n});let t={publishableKey:process.env.STRIPE_LIVE_PUBLISHABLE_KEY,secretKey:process.env.STRIPE_LIVE_SECRET_KEY,webhookSecret:process.env.STRIPE_LIVE_WEBHOOK_SECRET},i={FREE:process.env.STRIPE_LIVE_FREE_PRICE_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRICE_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID},n={FREE:process.env.STRIPE_LIVE_FREE_PRODUCT_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRODUCT_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID};process.env.STRIPE_LIVE_FREE_PRICE_ID,process.env.STRIPE_LIVE_STARTER_PRICE_ID,process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID,t.publishableKey&&t.publishableKey.substring(0,20),t.secretKey&&t.secretKey.substring(0,20),t.webhookSecret&&t.webhookSecret.substring(0,15)},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,580,9398,4745],()=>s(60230));module.exports=t})();