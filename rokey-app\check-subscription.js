const stripe = require('stripe')('***********************************************************************************************************');

async function checkSubscription() {
  try {
    const subscription = await stripe.subscriptions.retrieve('sub_1RmKu5C97XFBBUvd59vpORut');
    console.log('Subscription details:');
    console.log('ID:', subscription.id);
    console.log('Status:', subscription.status);
    console.log('Customer:', subscription.customer);
    console.log('Price ID:', subscription.items.data[0]?.price.id);
    console.log('Price amount:', subscription.items.data[0]?.price.unit_amount);
    console.log('Price currency:', subscription.items.data[0]?.price.currency);
    console.log('Price recurring:', subscription.items.data[0]?.price.recurring);
    
    // Check what tier this price ID should map to
    const priceId = subscription.items.data[0]?.price.id;
    console.log('\nPrice ID mapping:');
    console.log('Expected starter price ID: price_1RcekMC97XFBBUvdYnl0leWM');
    console.log('Actual price ID:', priceId);
    console.log('Match:', priceId === 'price_1RcekMC97XFBBUvdYnl0leWM');
  } catch (error) {
    console.error('Error:', error.message);
  }
}

checkSubscription();
