(()=>{var e={};e.id=3930,e.ids=[1489,3930],e.modules={2507:(e,r,t)=>{"use strict";t.d(r,{Q:()=>u,createSupabaseServerClientOnRequest:()=>n});var s=t(34386),i=t(44999);async function n(){let e=await (0,i.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:r=>e.get(r)?.value,set(r,t,s){try{e.set({name:r,value:t,...s})}catch(e){}},remove(r,t){try{e.set({name:r,value:"",...t})}catch(e){}}}})}function u(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:r=>e.cookies.get(r)?.value,set(e,r,t){},remove(e,r){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8932:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>P,routeModule:()=>S,serverHooks:()=>T,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{GET:()=>d});var i=t(96559),n=t(48088),u=t(37719),o=t(32190),a=t(2507),c=t(80726),p=t(64745),E=t(94473);let I=new p.A(E.Lj.secretKey,{apiVersion:"2025-02-24.acacia"});async function _(e){try{let r=(0,c.H)(),{data:t,error:s}=await r.from("subscriptions").select("stripe_subscription_id").eq("user_id",e).single();if(s||!t||!t.stripe_subscription_id)return"free";let i=await I.subscriptions.retrieve(t.stripe_subscription_id);if("active"!==i.status)return"free";let n=i.items.data[0]?.price.id,u=function(e){if(!e)return"free";switch(e){case E.Dm.FREE:return"free";case E.Dm.STARTER:return"starter";case E.Dm.PROFESSIONAL:return"professional";case E.Dm.ENTERPRISE:return"enterprise";default:return"free"}}(n);return R(e,u,i).catch(e=>console.error("Background database update failed:",e)),u}catch(e){return"free"}}async function R(e,r,t){try{let s=(0,c.H)();await s.from("subscriptions").update({tier:r,status:t.status,current_period_start:new Date(1e3*t.current_period_start).toISOString(),current_period_end:new Date(1e3*t.current_period_end).toISOString(),cancel_at_period_end:t.cancel_at_period_end,is_free_tier:"free"===r,updated_at:new Date().toISOString()}).eq("user_id",e),await s.from("user_profiles").update({subscription_tier:r,updated_at:new Date().toISOString()}).eq("id",e)}catch(e){}}async function d(e){let r=(0,a.Q)(e);try{let{data:{user:e},error:t}=await r.auth.getUser();if(t||!e)return o.NextResponse.json({error:"Unauthorized"},{status:401});let s=await _(e.id);return o.NextResponse.json({tier:s})}catch(e){return o.NextResponse.json({error:"Internal server error"},{status:500})}}let S=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/user/subscription-tier/route",pathname:"/api/user/subscription-tier",filename:"route",bundlePath:"app/api/user/subscription-tier/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\user\\subscription-tier\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:h,serverHooks:T}=S;function P(){return(0,u.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:h})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80726:(e,r,t)=>{"use strict";t.d(r,{H:()=>i});var s=t(39398);function i(){return(0,s.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}})}},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94473:(e,r,t)=>{"use strict";t.d(r,{Dm:()=>i,Lj:()=>s,Zu:()=>n});let s={publishableKey:process.env.STRIPE_LIVE_PUBLISHABLE_KEY,secretKey:process.env.STRIPE_LIVE_SECRET_KEY,webhookSecret:process.env.STRIPE_LIVE_WEBHOOK_SECRET},i={FREE:process.env.STRIPE_LIVE_FREE_PRICE_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRICE_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID},n={FREE:process.env.STRIPE_LIVE_FREE_PRODUCT_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRODUCT_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID};process.env.STRIPE_LIVE_FREE_PRICE_ID,process.env.STRIPE_LIVE_STARTER_PRICE_ID,process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID,s.publishableKey&&s.publishableKey.substring(0,20),s.secretKey&&s.secretKey.substring(0,20),s.webhookSecret&&s.webhookSecret.substring(0,15)},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,9398,3410,4745],()=>t(8932));module.exports=s})();