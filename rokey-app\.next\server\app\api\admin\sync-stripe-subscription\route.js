(()=>{var e={};e.id=2275,e.ids=[2275],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11502:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{POST:()=>p});var i=t(96559),o=t(48088),a=t(37719),n=t(32190);let u=(0,t(39398).createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function p(e){try{let{email:r,tier:t}=await e.json();if(!r||!t)return n.NextResponse.json({error:"Email and tier are required"},{status:400});if(!["free","starter","professional","enterprise"].includes(t))return n.NextResponse.json({error:"Invalid tier"},{status:400});let{data:s,error:i}=await u.from("auth.users").select("id, email").eq("email",r).single();if(i||!s)return n.NextResponse.json({error:"User not found"},{status:404});let{data:o,error:a}=await u.from("subscriptions").select("*").eq("user_id",s.id).single();if(a||!o)return n.NextResponse.json({error:"Database subscription not found"},{status:404});let{error:p}=await u.from("subscriptions").update({tier:t,is_free_tier:"free"===t,updated_at:new Date().toISOString()}).eq("user_id",s.id);if(p)return n.NextResponse.json({error:"Failed to update subscription"},{status:500});let{error:d}=await u.from("user_profiles").update({subscription_tier:t,user_status:"active",updated_at:new Date().toISOString()}).eq("id",s.id);if(d)return n.NextResponse.json({error:"Failed to update user profile"},{status:500});let{error:c}=await u.auth.admin.updateUserById(s.id,{user_metadata:{payment_status:"free"===t?"free":"completed",plan:t}});return n.NextResponse.json({success:!0,user:{id:s.id,email:s.email},before:{db_tier:o.tier,db_status:o.status,db_is_free_tier:o.is_free_tier},after:{new_tier:t,is_free_tier:"free"===t}})}catch(e){return n.NextResponse.json({error:"Failed to sync subscription",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/admin/sync-stripe-subscription/route",pathname:"/api/admin/sync-stripe-subscription",filename:"route",bundlePath:"app/api/admin/sync-stripe-subscription/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\admin\\sync-stripe-subscription\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:l,serverHooks:x}=d;function f(){return(0,a.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:l})}},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,9398],()=>t(11502));module.exports=s})();