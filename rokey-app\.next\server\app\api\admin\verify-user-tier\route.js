(()=>{var e={};e.id=2376,e.ids=[2376],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12181:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>p});var i=t(96559),o=t(48088),u=t(37719),a=t(32190);let n=(0,t(39398).createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function p(e){try{let{searchParams:r}=new URL(e.url),t=r.get("email");if(!t)return a.NextResponse.json({error:"Email parameter required"},{status:400});let{data:s,error:i}=await n.from("auth.users").select("id, email").eq("email",t).single();if(i||!s)return a.NextResponse.json({error:"User not found"},{status:404});let{data:o,error:u}=await n.from("user_profiles").select("*").eq("id",s.id).single(),{data:p,error:l}=await n.from("subscriptions").select("*").eq("user_id",s.id).single();return a.NextResponse.json({user:{id:s.id,email:s.email},profile:o||null,subscription:p||null,errors:{profile:u?.message||null,subscription:l?.message||null}})}catch(e){return a.NextResponse.json({error:"Failed to verify user tier",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let l=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/admin/verify-user-tier/route",pathname:"/api/admin/verify-user-tier",filename:"route",bundlePath:"app/api/admin/verify-user-tier/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\admin\\verify-user-tier\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:x}=l;function m(){return(0,u.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,9398],()=>t(12181));module.exports=s})();